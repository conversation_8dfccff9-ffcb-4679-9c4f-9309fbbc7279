import { Component, OnInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { HeaderComponent } from '../login/header/header.component';
import { FloatLabelModule } from 'primeng/floatlabel';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ConfirmButtonComponent } from '../buttons/confirm-button/confirm-button.component';
import { FormTemplateComponent } from '../login/form-template/form-template.component';
import { ReactiveFormsModule } from '@angular/forms';
import { LoadingComponent } from '../login/loading/loading.component';
import { FocusDirective } from '../../directives/focus.directive';

@Component({
    selector: 'app-magic',
    imports: [
        CommonModule,
        MainScreenComponent,
        HeaderComponent,
        FloatLabelModule,
        InputTextModule,
        IconFieldModule,
        InputIconModule,
        ConfirmButtonComponent,
        FormTemplateComponent,
        ReactiveFormsModule,
        LoadingComponent,
        FocusDirective,
    ],
    templateUrl: './magic.component.html',
    styleUrl: './magic.component.scss'
})
export class MagicComponent implements OnInit, OnDestroy {
  @ViewChild('cursor') cursorRef!: ElementRef;
  @ViewChild('magicButton') magicButtonRef!: ElementRef;

  showEmailContent = false;
  showCopiedBadge = false;
  showContextMenu = false;
  private progressInterval: any;
  
  ngOnInit() {
    const cursorEl = this.cursorRef?.nativeElement;
    if (cursorEl) {
      cursorEl.style.opacity = '0';
      cursorEl.style.left = '1.5rem';
      cursorEl.style.top = '1.5rem';
    }
    this.startProgress();
    setTimeout(() => this.startAnimation(), 1000);
  }

  ngOnDestroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }

  private startProgress() {
    const updateProgress = () => {
      const bar = document.querySelector('.progress-bar') as HTMLElement;
      if (bar) {
        bar.classList.remove('animate');
        requestAnimationFrame(() => {
          bar.classList.add('animate');
        });
      }
    };

    updateProgress();
    this.progressInterval = setInterval(updateProgress, 10500); // 7.5s animação + 3s pausa
  }

  async startAnimation() {
    
    const cursorEl = this.cursorRef.nativeElement;
    
    // Fade in do cursor
    cursorEl.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
    cursorEl.style.opacity = '1';
    await this.delay(1200);

    // Move para o primeiro email e mostra conteúdo
    cursorEl.style.transform = 'translate(80px, 100px)';
    await this.delay(800);
    this.showEmailContent = true;
    await this.delay(1000);

    // Move para o botão
    const magicButtonEl = this.magicButtonRef.nativeElement;
    const rect = magicButtonEl.getBoundingClientRect();
    const cardRect = cursorEl.parentElement?.getBoundingClientRect();
    
    if (cardRect) {
      const x = rect.left - cardRect.left + rect.width/2;
      const y = rect.top - cardRect.top + rect.height/2;
      
      cursorEl.style.transform = `translate(${x}px, ${y}px)`;
    }
    await this.delay(1000);
    
    // Menu de contexto e cópia
    this.showContextMenu = true;
    await this.delay(1500);
    
    this.showContextMenu = false;
    await this.delay(500);
    this.showCopiedBadge = true;
    await this.delay(1500);

    // Reset
    this.showEmailContent = false;
    this.showCopiedBadge = false;
    cursorEl.style.transition = 'opacity 0.8s ease';
    cursorEl.style.opacity = '0';

    // Espera 3s e recomeça
    await this.delay(3000);
    this.startAnimation();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}