<app-main-screen>
  <app-header></app-header>
  <app-form-template class="fixed-size-form">
    <div class="container">
      <section class="form-template">
        <div>
          <h1 class="title-h1">Verify Your Identity</h1>
          <p class="title-h60">Copy the link from the button in the email we sent and paste it below to continue.</p>
        </div>

        <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
          <section class="data-section">
            <div class="otp-code" [class.input-focus]="isFocused" [class.filled]="formGroup.get('magicLink')?.value">
              <div class="loading-indicator" *ngIf="isLoading">
                <div class="spinner"></div>
              </div>
              <div class="error-indicator" *ngIf="showError">✕</div>
              <div class="code-input">
                <input 
                  type="text" 
                  id="code" 
                  formControlName="magicLink"
                  (focus)="onInputFocus()"
                  (blur)="onInputBlur()"
                  (input)="onInput($event)"
                  (paste)="onPaste($event)"/>
                <label for="code" id="fLabel" [class.floating]="isFocused || formGroup.get('magicLink')?.value">Paste magic link*</label>
              </div>
            </div>
            <p class="invalid-otp" *ngIf="invalidLink">Invalid link, please try again</p>
            <p class="invalid-otp" *ngIf="showTypingError">You can only paste the magic link here</p>
          </section>
        </form>
      </section>

      <div class="magic-container">
        <app-magic></app-magic>
      </div>
    </div>
  </app-form-template>
</app-main-screen>