import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Detect } from '../interfaces/detect';

@Injectable({
    providedIn: 'root',
})
export class HttpService {

    constructor(private httpClient: HttpClient) { }

    public httpPost(url: string, httpHeaders: HttpHeaders | null, postData: object): Observable<HttpResponse<any>> {
        return this.httpClient.post<any>(url, postData, { headers: httpHeaders || new HttpHeaders(), observe: "response" as const });
    }

    public httpGet(url: string, httpHeaders: HttpHeaders | null): Observable<HttpResponse<any>> {
        return this.httpClient.get<any>(url, { headers: httpHeaders || new HttpHeaders(), observe: "response" as const });
    }
}
