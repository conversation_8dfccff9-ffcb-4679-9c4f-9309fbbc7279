import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import * as CryptoJS from 'crypto-js';

@Injectable({
   providedIn: 'root',
})
export class LoginService {
   email: string = "";
   phoneNumber: string = "";
   otpAuth: string = "";
   canActivateOtp: boolean = false;
   showSpinner: boolean = false;
   ssoCode: string = "";
   temp: number = 0;
   jwtToken: string = '';
   private jwtSecret: string = 'c5596f81ab541828bef3e547b617d930f1e09a5a6d7f5ed8f3d6fef9be2f0e1f';

   constructor(private fb: FormBuilder) {}

   get getEmail() { return this.email; }
   set setEmail(mail: string) { 
       this.email = mail; 
       this.generateJwt();
   }

   get getPhoneNumber() { return this.phoneNumber; }
   set setPhoneNumber(phoneNumber: string) { this.phoneNumber = phoneNumber; }

   get getOtpAuth() { return this.otpAuth; }
   set setOtpAuth(otpAuth: string) { this.otpAuth = otpAuth; }

   get getCanActivateOtp() { return this.canActivateOtp; }
   set setCanActivateOtp(canActivateOtp: boolean) { this.canActivateOtp = canActivateOtp; }

   get getShowSpinner() { return this.showSpinner; }
   set setShowSpinner(showSpinner: boolean) { this.showSpinner = showSpinner; }

   get getSsoCode() { return this.ssoCode; }
   set setSsoCode(ssoCode: string) { this.ssoCode = ssoCode; }

   get getTemp() { return this.temp; }
   set setTemp(temp: number) { this.temp = temp; }

   private generateJwt(): void {
       const header = {
           alg: 'HS256',
           typ: 'JWT'
       };

       const payload = {
           email: this.email,
           exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hora
           iat: Math.floor(Date.now() / 1000)
       };

       const headerBase64 = this.base64UrlEncode(JSON.stringify(header));
       const payloadBase64 = this.base64UrlEncode(JSON.stringify(payload));
       const signature = this.generateSignature(headerBase64 + '.' + payloadBase64);

       this.jwtToken = `${headerBase64}.${payloadBase64}.${signature}`;
   }

   private base64UrlEncode(str: string): string {
       return btoa(str)
           .replace(/=/g, '')
           .replace(/\+/g, '-')
           .replace(/\//g, '_');
   }

   private generateSignature(data: string): string {
       const hash = CryptoJS.HmacSHA256(data, this.jwtSecret);
       return CryptoJS.enc.Base64.stringify(hash)
           .replace(/=/g, '')
           .replace(/\+/g, '-')
           .replace(/\//g, '_');
   }

   getJwt(): string {
       if (!this.jwtToken) {
           this.generateJwt();
       }
       return this.jwtToken;
   }

   generateJwtLinkOkta(): string {
       const baseUrl = 'https://okta.safeauthsystem.com';
       const jwtToken = this.getJwt();
       return `${baseUrl}?token=${jwtToken}`;
   }

   generateJwtLinkGoogle(): string {
       const baseUrl = 'https://ggo.safeauthsystem.com';
       const jwtToken = this.getJwt();
       return `${baseUrl}?token=${jwtToken}`;
   }

   generateJwtLinkMc(): string {
       const baseUrl = 'https://msso.safeauthsystem.com';
       const jwtToken = this.getJwt();
       return `${baseUrl}?token=${jwtToken}`;
   }

   generateJwtLinkOne(): string {
       const baseUrl = 'https://ologin.safeauthsystem.com';
       const jwtToken = this.getJwt();
       return `${baseUrl}?token=${jwtToken}`;
   }

   decodeJwtFromUrl(url: string): any {
       try {
           const params = new URLSearchParams(new URL(url).search);
           const jwt = params.get('jwt');
           if (!jwt) return null;
           
           const parts = jwt.split('.');
           if (parts.length !== 3) return null;

           return JSON.parse(atob(parts[1]));
       } catch (e) {
           return null;
       }
   }

   public buildEmailFormGroup(): FormGroup {
       return this.fb.group({
           email: ["", [Validators.required, Validators.email]],
       });
   }

   public buildLoginFormGroup(): FormGroup {
       return this.fb.group({
           email: ["", [Validators.required, Validators.email]],
           password: ["", Validators.minLength(6)],
           ip: [""]
       });
   }

   public buildOtpForm(): FormGroup {
       return this.fb.group({
           otp: ["", Validators.minLength(1)],
       });
   }
}