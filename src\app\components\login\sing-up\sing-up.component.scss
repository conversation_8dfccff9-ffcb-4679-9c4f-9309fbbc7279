.title {
    height: auto;
    margin-top: var(--dim-step4);
    font-family: <PERSON>oma<PERSON>;
    line-height: 44px;
    font-size: var(--text-size-xxh);
    letter-spacing: 0.02em;
    width: 100%;
}

h1 {
    padding: 20px;
    margin: 0 !important;
}

.forms {
    width: 100%;
    height: auto;
    max-height: 33.75rem;
    padding: 20px;
}

.blocked-email {
    width: 100%;
    height: 3.25rem;
    line-height: 44px;
    border: 1px solid #c9cace;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 1rem;
    align-items: center;
    margin-bottom: 0.8rem;
    color: #A1A1AE;
    font-weight: 500;

    a {
        text-decoration: none;
        color: #7300cd;
        font-size: 14px;
    }
}

.password-input {
    width: 100%;
    height: 3.25rem;
    border: 1px solid #c9cace;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .p-input {
        display: flex;
        flex-direction: column;
        width: 92%;

        input {
            width: 100%;
            height: 2.5rem;
            border: none;
            outline: none;
            border-radius: 10px;
            margin-left: 1rem;
            margin-top: 0.3rem;
        }

        label {
            position: relative;
            font-size: 16px;
            color: #65676e;
            top: -15px;
            margin-left: 1rem;
            padding: 0 0.4rem 0 0.4rem;
            transition: top 0.2s;
            width: fit-content;
        }

        .floating {
            top: -42px !important;
            background-color: white;
            color: #7301cd;
            font-size: 14px;
            left: -8px;
        }
    }

    .icon {
        width: 8%;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.25s ease-in-out,
            box-shadow 0.25s ease-in-out;
        border-radius: 0 10px 10px 0;
    }

    .icon:hover {
        background-color: #e5e5e5;
    }
}

.input-focus {
    border: 2px solid #7301cd;
}

.reset-password {
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 400;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    color: #7300cd;
}

.c209307e0 {
    margin-top: 2rem;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: 2rem;

    .lines {
        padding: 0 1rem 0 1rem;
    }
}

.c209307e0::after,
.c209307e0::before {
    content: '';
    border-bottom: 1px solid #c9cace;
    border-bottom: 1px solid #c9cace;
    flex: 1 0 auto;
    height: auto;
    width: 30%;
    margin: 0;
}

.google-login {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    width: 100%;
    height: 3.25rem;
    background-color: #4286f5;
    border-radius: 10px;
    border: 1px solid #c9cace;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    img {
        position: absolute;
        left: 10px;
        background-color: white;
        border-radius: 10px;
        width: 2.8rem;
        height: 2.8rem;
        padding: 0.4rem;
    }
}
#resend {
    margin-top: 20px;
    // justify-self: center;
    align-self: center;
    margin-bottom: 0.7rem;
}
#log {
    font-weight: 600;
}
.google-login:hover {
    background-color: #e5e5e5;
}
