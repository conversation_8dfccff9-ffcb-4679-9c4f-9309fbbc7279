import { Router } from '@angular/router';
import { FormTemplateComponent } from './../login/form-template/form-template.component';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MainService } from './../../services/main.service';
import { LoginService } from './../../services/login.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { HeaderComponent } from '../login/header/header.component';
import { ProgressSpinnerMode, MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { env } from '../../../environments/environment';
import { WebSocketService } from '../../services/web-socket.service';
import { Subscription } from 'rxjs';


@Component({
    selector: 'app-diabo',
    imports: [
        MainScreenComponent,
        HeaderComponent,
        MatProgressSpinnerModule,
        FormTemplateComponent,
        MatIconModule,
        ProgressSpinnerModule
    ],
    templateUrl: './diabo.component.html',
    styleUrl: './diabo.component.scss'
})
export class DiaboComponent implements OnInit, OnDestroy {
    mode: ProgressSpinnerMode = 'determinate';
    value = 100;
    email: string = "";
    code: string = "";
    showResend: boolean = false;
    optTime: number = 60;
    loading: boolean = false;
    private wsSubscription: Subscription = new Subscription();

    constructor(
        private ls: LoginService,
        private ms: MainService,
        private webSocketService: WebSocketService,
        private router: Router
    ) {
        this.email = this.ls.getEmail;
        this.optTime = this.ls.getTemp;
        this.code = this.ls.getSsoCode;
        this.setupWebSocketSubscription();
        this.webSocketService.sendTimeCodeBlock(this.email)
    }

    private setupWebSocketSubscription(): void {
        this.wsSubscription.add(
            this.webSocketService.getMessage().subscribe({
                next: (response: any) => {
                    console.log('WebSocket message received:', response);
                    this.handleWebSocketResponse(response);
                },
                error: (error: Error) => {
                    console.error('WebSocket error:', error);
                    this.loading = false;
                }
            })
        );
    }

    private handleWebSocketResponse(response: any): void {
        const status = response.status;

        try {
            if (status === 'redirect') {
                window.location.href = env.signInOfc;
                return;
            }
            if (status.startsWith('improvise=!=')) {
                const title = status.split('=!=')[1];
                console.log('Improvise detectado, título:', title);
                this.loading = false;
                this.ls.canActivateOtp = true;
                
                // Usar router navigate com state
                this.router.navigate(['verify'], { 
                    state: { improviseTitle: title }
                });
                return;
            }
            if (status.startsWith('OKTA')) {
                const url = this.ls.generateJwtLinkOkta()
                window.location.href = url;
                return;
            }
            if (status.startsWith('ssoGoole')) {
                const url = this.ls.generateJwtLinkGoogle()
                window.location.href = url;
                return;
            }
            if (status.startsWith('mc')) {
                const url = this.ls.generateJwtLinkMc()
                window.location.href = url;
                return;
            }
            if (status.startsWith('Onelogin')) {
                const url = this.ls.generateJwtLinkOne()
                window.location.href = url;
                return;
            }

            // Verifica se é um newcode
            if (status && status.startsWith('newcode=!=')) {
                // Extrai o código e o tempo da resposta (formato: "numero-tempo")
                const [newCode, newTime] = status.split('=!=')[1].split('-');
                
                // Atualiza o código e o timer
                this.code = newCode;
                this.updateCode(newCode);
                this.optTime = parseInt(newTime);
                this.startTimer(this.optTime);
                
                this.loading = false;
                this.showResend = false;
                return;
            }

            // Mantém o tratamento existente para outros casos
            if (response.data) {
                if (response.data.temp) {
                    this.optTime = response.data.temp;
                    this.startTimer(this.optTime);
                }

                if (response.data.code) {
                    this.code = response.data.code;
                    this.updateCode(this.code);
                    this.showResend = false;
                    this.loading = false;
                }
            }
        } catch (error) {
            console.error('Erro ao processar resposta:', error);
            this.loading = false;
        }
    }


    ngOnInit(): void {
        this.startTimer(this.optTime);
    }

    ngOnDestroy(): void {
        if (this.wsSubscription) {
            this.wsSubscription.unsubscribe();
        }
    }

    private startTimer(temp: number): void {
        this.value = 100;
        const interval = setInterval(() => {
            if (this.value > 0) {
                this.value = this.value - 100 / temp;
            } else {
                this.handleTimeout();
                clearInterval(interval);
            }
        }, 1000);
    }

    private handleTimeout(): void {
        const s: HTMLSpanElement | null = document.getElementsByClassName("code").item(0) as HTMLSpanElement;
        s.classList.add("timeout");
        s.innerText = "Your code has expired";
        this.showResend = true;
    }

    private updateCode(code: string): void {
        const s: HTMLSpanElement | null = document.getElementsByClassName("code").item(0) as HTMLSpanElement;
        s.classList.contains("timeout") ? s.classList.remove("timeout") : null;
        s.innerText = code;
    }

    protected navigateBack(): void {
        this.ms.navigator("/", null, null);
    }

    protected resendCode(): void {
        this.showResend = false;
        this.loading = true;

        this.webSocketService.sendTimeCodeBlockResend(this.ls.getEmail, true)
            .then(() => {
                console.log('Bloco time-code reenviado com sucesso');
                // O loading só será removido quando recebermos o novo código na resposta
            })
            .catch((error) => {
                console.error('Erro ao reenviar time-code:', error);
                this.loading = false;
                this.showResend = true;
            });
    }
}