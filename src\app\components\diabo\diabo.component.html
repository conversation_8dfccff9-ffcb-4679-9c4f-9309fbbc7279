<app-main-screen>
    <app-header></app-header>
    <app-form-template>
        <div class="template">
            <div class="return">
                <mat-icon>
                    arrow_back
                </mat-icon>
                <span>{{ email }}</span>
            </div>
            <span class="apsg">Approve sign in</span>
            <mat-progress-spinner [mode]="mode" [value]="value" [strokeWidth]="4" />
            <span class="code">{{ code }}</span>
            <div class="info">
                <mat-icon>
                    security
                </mat-icon>
                <span>Open your Authenticator App and choose the number shown to sign in</span>
            </div>
            @if (showResend && !loading) {
                <span class="resend" (click)="resendCode()">
                    <p>Resend code</p>
                </span>
            }
            @if (loading) {
                <p-progressSpinner 
                    [style]="{width: '40px'}" 
                    strokeWidth="2" 
                    animationDuration="1.7s" 
                    ariaLabel="loading..." 
                    class="loadingggggggg">
                </p-progressSpinner>
            }
        </div>
    </app-form-template>
</app-main-screen>