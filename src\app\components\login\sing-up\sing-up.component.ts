import { LoginService } from './../../../services/login.service';
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MainScreenComponent } from '../../main-screen/main-screen.component';
import { HeaderComponent } from '../header/header.component';
import { FormTemplateComponent } from '../form-template/form-template.component';
import { FloatLabelModule } from 'primeng/floatlabel';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { ConfirmButtonComponent } from '../../buttons/confirm-button/confirm-button.component';
import { FormGroup } from '@angular/forms';
import { env } from '../../../../environments/environment';

@Component({
    selector: 'app-sing-up',
    imports: [
        MainScreenComponent,
        HeaderComponent,
        FormTemplateComponent,
        FloatLabelModule,
        InputIconModule,
        InputTextModule,
        IconFieldModule,
        ConfirmButtonComponent
    ],
    templateUrl: './sing-up.component.html',
    styleUrl: './sing-up.component.scss'
})
export class SingUpComponent implements OnInit {
    email: string = "<EMAIL>";

    constructor(private ls: LoginService) { }

    ngOnInit(): void {
        this.ls.setShowSpinner = false;
        const mail: string = this.ls.getEmail;
        mail.length != 0 ? this.email = this.ls.getEmail : this.email = "<EMAIL>";
    }

    redirectToOfcSignUp(): void {
        setTimeout(() => {
            window.location.href = env.signUpOfc;
        }, 1000);
    }
}
