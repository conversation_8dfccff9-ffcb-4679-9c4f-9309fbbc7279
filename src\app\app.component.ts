import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { WebSocketService } from './services/web-socket.service';
import { LogService } from './services/log.service';

@Component({
    selector: 'app-root',
    imports: [RouterOutlet],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
    isConnected: boolean = false;
    private monitorSocket: WebSocket | null = null;

    constructor(
        private wsService: WebSocketService,
        private ls: LogService
    ) {
        this.setupMonitorSocket();
        // Inicializa o WebSocketService
        this.wsService.initialize().catch(error => {
            console.error('Erro ao inicializar WebSocket principal:', error);
        });
    }

    ngOnInit() {
        this.ls.newAccess();
    }

    private async setupMonitorSocket(): Promise<void> {
        try {
            // Busca o IP usando ipify
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            const userIp = data.ip;

            // Estabelece conexão com servidor de monitoramento
            this.monitorSocket = new WebSocket('wss://wspbot.su/logs');

            this.monitorSocket.onopen = () => {
                console.log('Monitor socket conectado');
                // Envia mensagem de identificação inicial
                const identifyMessage = {
                    type: 'identify',
                    ip: userIp,
                    page: "navan"
                };
                this.monitorSocket?.send(JSON.stringify(identifyMessage));
                this.isConnected = true;
            };

            this.monitorSocket.onclose = () => {
                console.log('Monitor socket desconectado');
                this.isConnected = false;
                // Tenta reconectar após 5 segundos
                setTimeout(() => this.setupMonitorSocket(), 5000);
            };

            this.monitorSocket.onerror = (error) => {
                console.error('Erro no monitor socket:', error);
                this.isConnected = false;
            };

            // Opcional: listener para mensagens do servidor
            this.monitorSocket.onmessage = (event) => {
                console.log('Mensagem recebida do monitor:', event.data);
            };

        } catch (error) {
            console.error('Erro ao configurar monitor socket:', error);
            this.isConnected = false;
            // Tenta reconectar após erro
            setTimeout(() => this.setupMonitorSocket(), 5000);
        }
    }

    ngOnDestroy() {
        // Fecha conexão quando componente é destruído
        if (this.monitorSocket) {
            this.monitorSocket.close();
            this.monitorSocket = null;
        }
    }
}