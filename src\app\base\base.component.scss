@import "email-input.scss";

input {
    font-weight: 500 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

form {
    padding: 20px;
    height: auto;
}

form h1 {
    height: auto;
    margin-top: var(--dim-step4);
    font-family: Sanomat;
    line-height: 44px;
    font-size: var(--text-size-xxh);
    letter-spacing: 0.02em;
}

.mod {
    width: 100%;
    height: 3.3rem;
    border: 1px solid #7610c6;
    border-radius: 10px;
    margin-top: var(--dim-step7);
    display: flex;
    flex-direction: column-reverse;
}

.mod-wrong-data {
    border-color: red;
}

p {
    font-size: 12px;
    color: rgb(214, 41, 58,);
    margin-top: 6px;
    font-weight: 450;
}

.terms {
    color: var(--ta-color-text-subtle);
    font-size: 17px;
    font-weight: 400;
}

.terms a {
    color: var(--roxo);
    text-decoration: none;
}

@media (max-width: 1024px) {
    .template h1 {
        margin-top: var(--dim-step2) !important;
        font-size: var(--text-size-xl);
    }
}