.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 10px;
    height: 35vh; // Centraliza verticalmente na tela inteira
    background-color: #f8f9fa; // Fundo claro para contraste
  }
  
  .spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px; // Espaço entre o spinner e o texto
  
    .spinner-circle {
      width: 3rem;
      height: 3rem;
      border: 4px solid #a307a3;
      border-top: 4px solid transparent;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
    }
  }
  
  .text-loading {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  