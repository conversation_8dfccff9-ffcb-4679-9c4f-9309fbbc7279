.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: fit-content;
  z-index: 1000;
}

.title {
  height: auto;
  margin-top: var(--dim-step4);
  font-family: Sanomat;
  line-height: 44px;
  font-size: var(--text-size-xxh);
  letter-spacing: .02em;
  width: 100%;
}

h1 {
  padding: 40px 40px 24px;
  margin: 0 !important;
}

.forms {
  width: 100%;
  height: auto;
  max-height: 33.75rem;
  padding: 0px 40px 40px;
}

form {
  display: flex;
  flex-direction: column;
}

.blocked-email {
  width: 100%;
  height: 3.25rem;
  line-height: 44px;
  border: 1px solid #c9cace;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 1rem;
  align-items: center;
  margin-bottom: .8rem;

  a {
      text-decoration: none;
      color: #7300CD;
      font-size: 14px;
  }
}

.password-input {
  width: 100%;
  height: 3.25rem;
  border: 1px solid #c9cace;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .p-input {
      display: flex;
      flex-direction: column;
      width: 92%;
      height: fit-content;

      input {
          width: 100%;
          height: 5rem;
          border: none;
          outline: none;
          border-radius: 10px;
          margin-left: 1rem;
          margin-top: .5rem;
          height: 30px;
          font-size: 16px;
        
      }

      label {
          position: relative;
          font-size: 16px;
          color: #65676E;
          top: -20px;
          margin-left: 1rem;
          padding: 0 .4rem 0 .4rem;
          transition: top .2s;
          width: fit-content;
      }

      .floating {
          top: -47px !important;
          background-color: white;
          color: #7301CD;
          font-size: 14px;
          left: -8px;
      }
  }

  .icon {
      width: 8%;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: background-color .25s ease-in-out, box-shadow .25s ease-in-out;
      border-radius: 0 10px 10px 0;
  }

  .icon:hover {
      background-color: #E5E5E5;
      cursor: pointer;
  }
}

.wrong-credentials {
  font-size: 12px;
  color: red;
  margin-top: 6px;
  font-weight: 400;
}

.input-focus {
  border: 2px solid #7301CD;
}

.reset-password {
  text-decoration: none;
  font-size: .95rem;
  font-weight: 400;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  color: #7300CD;
  width: fit-content;
}

.c209307e0 {
  margin-top: 2rem;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
  margin-bottom: 2rem;

  .lines {
      padding: 0 1rem 0 1rem;
  }
}

.c209307e0::after, .c209307e0::before {
  content: "";
  border-bottom: 1px solid #c9cace;
  border-bottom: 1px solid #c9cace;
  flex: 1 0 auto;
  height: auto;
  width: 30%;
  margin: 0;
}

.google-login {
  width: 100%;
  height: 3.25rem;
  background-color: white;
  border-radius: 10px;
  border: 1px solid #c9cace;
  font-size: 16px;
  font-weight: 200;
  display: flex;
  align-items: center;
  justify-content: start;
  cursor: pointer;
  transition: background-color .2s ease;
  
  img {
      width: 2.8rem;
      height: 2.8rem;
      padding: .4rem;
  }
}

.google-login:hover {
  background-color: #E5E5E5;
}