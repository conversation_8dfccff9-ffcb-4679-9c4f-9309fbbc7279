<app-main-screen *ngIf="!loading">
    <app-header></app-header>
    <app-form-template>
        <form class="auth-email" [formGroup]="emailForm" (ngSubmit)="onSubmit()">
            <h1>Welcome to Navan!</h1>
            <div class="mod">
                <div class="strange">
                    <span class="placeholder" id="fLabel">Email</span>
                </div>
                <input type="email" id="input-email" formControlName="email" required autofocus (focus)="onEmailInputFocus()" (blur)="onEmailInputBlur()" />
            </div>
            <p *ngIf="showError">{{ errorMessage }}</p>
            <div class="space-between" [ngStyle]="emailForm.valid ? {marginTop: '18px'} : {marginTop: '0'}"></div>
            <span class="terms">
                By continuing, I acknowledge the <a href="https://navan.com/privacy">Navan Privacy Policy</a>.
            </span>
            <div class="space-between" style="margin-bottom: 28px;"></div>
            <app-confirm-button [canActivate]="emailForm.valid">Continue</app-confirm-button>
        </form>
    </app-form-template>

</app-main-screen>

<app-loading class="loading" *ngIf="loading"></app-loading>