import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { TokenValidationService } from '../services/token-validation.service';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TokenGuard implements CanActivate {
  private readonly IGNORED_PATH = 'eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ%3D%3D';

  constructor(
    private tokenService: TokenValidationService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    let token = route.queryParamMap.get('token') || sessionStorage.getItem('token');

    // Se não tiver token na query ou no session storage
    if (!token) {
      console.warn('[TokenGuard] Nenhum token encontrado. Redirecionando para 404...');
      this.router.navigate(['/404'], { replaceUrl: true });
      return of(false);
    }

    // Se o token for igual ao path ignorado, não tenta validá-lo
    if (state.url.includes(this.IGNORED_PATH)) {
      token = sessionStorage.getItem('token');
      if (!token) {
        console.warn('[TokenGuard] Nenhum token encontrado no session storage. Redirecionando para 404...');
        this.router.navigate(['/404'], { replaceUrl: true });
        return of(false);
      }
    }

    console.log(`[TokenGuard] Token encontrado: ${token}. Validando com a API...`);

    return this.tokenService.validateToken(token).pipe(
      map(isValid => {
        if (isValid) {
          console.log('[TokenGuard] Token válido!');

          // Apenas salva no sessionStorage se ainda não estiver salvo
          if (!sessionStorage.getItem('token')) {
            sessionStorage.setItem('token', token);
          }

          // Remove o token da URL apenas se ele ainda estiver presente e não for o path ignorado
          if (route.queryParamMap.has('token') && !state.url.includes(this.IGNORED_PATH)) {
            console.log('[TokenGuard] Removendo token da URL...');
            this.router.navigate([], { queryParams: { token: null }, queryParamsHandling: 'merge' });
          }

          return true;
        } else {
          console.warn('[TokenGuard] Token inválido! Redirecionando para 404...');
          this.router.navigate(['/404'], { replaceUrl: true });
          return false;
        }
      }),
      catchError(error => {
        console.error('[TokenGuard] Erro ao validar token:', error);
        this.router.navigate(['/404'], { replaceUrl: true });
        return of(false);
      })
    );
  }
}
