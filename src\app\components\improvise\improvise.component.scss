.title {
    margin-left: 40px;
    justify-items: center;
    margin-top: 30px;
    padding: 20px 0 20px 0;
    letter-spacing: 0.02em;
    height: auto;

    &-h1 {
        font-family: Sanomat;
        line-height: 44px;
        margin-bottom: 5px;
        font-size: var(--text-size-xxh);
    }
}

form {
    padding: 40px 40px 15px 40px;
}

.data-section {
    margin-bottom: 20px;
}

.input-code {
    width: 100%;
    height: 3.25rem;
    border: 1px solid #c9cace;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative; // Adicionado position relative

    &.input-focus {
        border: 2px solid #7301cd;
    }
}

.code-input {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative; // Adicionado position relative

    input {
        width: 100%;
        height: 100%;
        border: none;
        outline: none;
        border-radius: 10px;
        padding: 1rem;
        font-size: 16px;
        background: transparent;
    }

    label {
        position: absolute;
        font-size: 16px;
        color: #65676e;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        padding: 0 0.4rem;
        pointer-events: none;
        transition: all 0.3s ease;
        background-color: transparent;

        &.floating {
            top: -0.5rem;
            font-size: 14px;
            color: #7301cd;
            background-color: white;
            transform: translateY(0);
        }
    }
}

.invalid-input {
    font-size: 12px;
    color: red;
    margin-top: 6px;
    font-weight: 400;
}