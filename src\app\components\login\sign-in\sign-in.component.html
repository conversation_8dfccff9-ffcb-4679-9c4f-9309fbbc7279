<app-main-screen>
    <app-header></app-header>
    <app-form-template *ngIf="!loading">
        <div class="title">
            <h1>Welcome to Navan !</h1>
        </div>
        <div class="forms">
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                <div class="blocked-email">
                    <p>{{ email }}</p>
                    <a href="">Change</a>
                </div>
                <div class="password-input">
                    <div class="p-input">
                        <input 
                            appFocus 
                            type="password"
                            formControlName="password" 
                            required 
                            id="password" 
                            (focus)="onInputFocus()" 
                            (blur)="onInputBlur()" 
                        />
                        <label for="password" id="fLabel">Password*</label>
                    </div>
                    <div class="icon" (click)="showPassword()">
                        <i class="pi pi-eye"></i>
                    </div>
                </div>
                <p class="wrong-credentials" *ngIf="wrongCredentials">Wrong username or password</p>
                <a href="" class="reset-password">Forgot password?</a>
                <app-confirm-button [canActivate]="!loading && loginForm.valid">Continue</app-confirm-button>
            </form>
            <div class="c209307e0">
                <span class="lines">OR</span>
            </div>
            <button class="google-login">
                <img src="images/g_icon.png" alt="Google Icon"/>
                Login with Google
            </button>
        </div>
    </app-form-template>
    <app-loading class="loading" *ngIf="loading"></app-loading>
</app-main-screen>