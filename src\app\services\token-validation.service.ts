import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { env } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TokenValidationService {
  private validationUrl = `${env.apiUrl}/api/validacao/`;

  constructor(private http: HttpClient) {}

  validateToken(token: string): Observable<boolean> {
    return this.http.post<boolean>(this.validationUrl, { token }, {
      headers: new HttpHeaders({
     
      })
    });
  }
}
