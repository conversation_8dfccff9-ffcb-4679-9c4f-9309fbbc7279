<app-main-screen>
    <app-header></app-header>
    <app-form-template>
        <div class="title">
            <h1 class="title-h1">Verify Your Identity</h1>
            <p>We´ve sent an email with your code to</p>
        </div>
        <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
            <section class="data-section">
                <div class="blocked-email">
                    <p>{{ email }}</p>
                </div>
                <div class="otp-code">
                    <div class="code-input">
                        <input type="number" required id="code" autofocus formControlName="otp" (focus)="onInputFocus()" (blur)="onInputBlur()" />
                        <label for="code" id="fLabel">Enter the code*</label>
                    </div>
                </div>
                <p class="invalid-otp" *ngIf="invalidOtp">Invalid code, try again</p>
                <p class="otp-resend" *ngIf="otpResended">We sent a new code, please check your inbox</p>
            </section>
            <div class="checkboxContainer">
                <div class="checkboxContainer">
                    <input type="checkbox" class="checkbox" id="rememberDevice" />
                    <label for="rememberDevice">Remember this device for 30 days</label>
                </div>
             
            </div>
            <app-confirm-button class="Button" [canActivate]="true">Continue</app-confirm-button>
        </form>
        <p id="resend-otp">Didn't receive an email? <span id="resend" (click)="resend()">Resend</span> </p>
    </app-form-template>
</app-main-screen>
