.strange {
    transform: translateY(-20px) perspective(100px) translateZ(0.001px);
    font-size: var(--text-size-xs);
    font-family: "NeueHaasGroteskTXPro", Helvetica;
    white-space: nowrap;
    position: absolute;
    margin-left: 12px;
    height: 1.2em;
    color: var(--ta-color-text-subtle);
    transform: translateY(-50%) perspective(100px);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1),
    font-size 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transition-property: transform, font-size;
    transition-duration: 0.4s, 0.4s;
    transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1),
    cubic-bezier(0.165, 0.84, 0.44, 1);
    transition-delay: 0s, 0s;
    pointer-events: none;
    max-width: 95%;

    .placeholder {
    overflow: hidden;

    font-size: 16px;
    font-family: var(--main-font);
    white-space: nowrap;
    text-align: left;
    width: 100%;
    position: relative;
    top: -5%;
    }

    .placeholder::after,
    .placeholder-after::after {
    content: "";
    position: absolute;
    right: -9px;
    top: 30%;
    width: 5px;
    height: 5px;
    background-color: var(--ta-color-text-warning);
    border-radius: 50%;
    transform: translateY(-50%);
    z-index: 1;
    }

    .placeholder-after::after {
    top: 10%;
    }

    .floating {
    font-size: 12px;
    position: relative;
    top: -90%;
    color: #d6293a !important;
    }

    .placeholder-valid {
    color: #68687d;
    }
}

input {
    border: none;
    width: 95%;
    height: 75%;
    margin-left: 12px;
    font-weight: 500;
    font-family: var(--main-font);
    font-size: var(--text-size-medium);
    color: var(--ta-color-text-default);
    font-weight: 700;
}

input:focus {
    outline: none;
}
