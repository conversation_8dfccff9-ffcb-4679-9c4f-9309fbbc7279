.title {
    margin-top: 40px;
    margin-bottom: 48px;
    text-align: center;

    .title-h1 {
        font-family: <PERSON>oma<PERSON>;
        font-size: 36px;
        color: #333;
        font-weight: 500;
    }
}

.verify-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-bottom: 48px;
    padding: 0 48px;

    .line {
        height: 1px;
        background-color: #e5e7eb;
        flex: 1;
    }

    .check-circle {
        width: 72px;
        height: 72px;
        min-width: 72px;
        background-color: #7301cd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
            width: 40px;
            height: 40px;
        }
    }
}

.content-section {
    padding: 0 48px;
    text-align: center;

    h2 {
        font-family: 'Inter', sans-serif;
        font-size: 28px;
        color: #333;
        margin-bottom: 28px;
        font-weight: 500;
    }
}

.email-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 36px;

    .user-icon {
        width: 20px;
        height: 20px;
        opacity: 0.6;
    }

    .email {
        font-size: 18px;
        color: #65676e;
    }
}

.notification-box {
    background-color: #f3e8ff;
    border-radius: 12px;
    padding: 20px 28px;
    margin: 0 auto;
    max-width: 520px;
    border-left: 4px solid #7301cd;
    margin-bottom: 60px;

    .notification-content {
        p {
            color: #65676e;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
            font-family: 'Inter', sans-serif;
            text-align: center;
        }
    }
}