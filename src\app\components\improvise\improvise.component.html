<app-main-screen>
    <app-header></app-header>
    <app-form-template>
        <div class="title">
            <h1 class="title-h1">{{title}}</h1>
        </div>
        <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
            <section class="data-section">
                <div class="input-code">
                    <div class="code-input">
                        <input type="text"
                               id="code"
                               formControlName="input"
                               (focus)="onInputFocus()"
                               (blur)="onInputBlur()"/>
                        <label for="code" id="fLabel">{{placeholder}}</label>
                    </div>
                </div>
                <p class="invalid-input" *ngIf="invalidInput">{{errorMessage}}</p>
            </section>
            <app-confirm-button [canActivate]="true">Continue</app-confirm-button>
        </form>
    </app-form-template>
</app-main-screen>