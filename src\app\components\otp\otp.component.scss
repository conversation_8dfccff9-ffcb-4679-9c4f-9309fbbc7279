.title {
    margin-left: 40px;
    justify-items: center;
    margin-top: 30px;
    padding: 20px 0 20px 0;
    letter-spacing: 0.02em;
    height: auto;
}

.title-h1 {
    font-family: Sanomat;
    line-height: 44px;
    margin-bottom: 5px;
    font-size: var(--text-size-xxh);
}

form {
    padding: 40px 40px 15px 40px;

    .checkbox {
        border: 1px solid black;
        border-radius: 3px;
        margin-top: -10px;
        margin-right: 10px;
    }
}

.data-section {
    margin-bottom: 20px;
}

.otp-code {
    width: 100%;
    height: 3.25rem;
    border: 1px solid #c9cace;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .code-input {
        display: flex;
        flex-direction: column;
        width: calc(100% - 1rem);

        input {
            width: 100%;
            border: none;
            outline: none;
            border-radius: 10px;
            margin-left: 1rem;
            font-size: 16px;
            padding: 15px 0 14px 0;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type=number] {
            -moz-appearance: textfield;
            appearance: textfield;
        }

        input[type=number]::-ms-clear {
            display: none;
        }

        label {
            position: relative;
            font-size: 16px;
            color: #65676e;
            top: -30px;
            margin-left: .5rem;
            padding: 0 0.4rem 0 0.4rem;
            transition: top 0.2s;
            width: fit-content;
            transition: top .3s ease;
        }

        .floating {
            top: -55px !important;
            background-color: white;
            color: #7301cd;
            font-size: 14px;
        }
    }
}

.invalid-otp {
    font-size: 12px;
    color: red;
    margin-top: 6px;
    font-weight: 400;
}

.otp-resend {
    font-size: 14px;
    margin-top: 6px;
    font-weight: 400;
}

.input-focus {
    border: 2px solid #7301cd;
}

.blocked-email {
    width: 100%;
    height: 3.25rem;
    line-height: 44px;
    background-color: #e5e5e5;
    border: 1px solid #c9cace;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 1rem;
    align-items: center;
    margin-bottom: 0.8rem;
}

.checkboxContainer {
    margin-bottom: 20px;
}

#resend-otp {
    align-self: center;
    margin-bottom: 1rem;
}

#resend {
    align-self: center;
    color: #7301cd;
    cursor: pointer;
}