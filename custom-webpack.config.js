const JavaScriptObfuscator = require('webpack-obfuscator');

module.exports = (config, options) => {
  if (config.mode === 'production') {
    config.plugins.push(
      new JavaScriptObfuscator({
        rotateStringArray: true,
        stringArray: true,
        stringArrayEncoding: ['base64'],
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 0.4,
        renameGlobals: true,
        splitStrings: true,
        unicodeEscapeSequence: true
      })
    );
  }
  return config;
};