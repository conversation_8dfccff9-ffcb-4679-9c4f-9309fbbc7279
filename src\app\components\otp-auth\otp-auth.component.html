<app-main-screen>
    <app-header></app-header>
    <app-form-template>
        <div class="title">
            <h1 class="title-h1">Verify Your Identity</h1>
            <p>Check your preferred one-time password app to get a code.</p>
        </div>
        <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
            <section class="data-section">
                <div class="otp-code">
                    <div class="code-input">
                        <input type="text" required id="code" autofocus formControlName="otp" (focus)="onInputFocus()" (blur)="onInputBlur()"/>
                        <label for="code" id="fLabel">Enter your unique code*</label>
                    </div>
                </div>
                <p class="invalid-otp" *ngIf="invalidOtp">Invalid code, try again</p>
            </section>
            <div class="checkboxContainer">
                <input type="checkbox" class="checkbox" id="rememberDevice" />
                <label for="rememberDevice">Remember this device for 30 days</label>
            </div>
            <app-confirm-button class="Button" [canActivate]="true">Continue</app-confirm-button>
        </form>
        <p id="resend" (click)="tryAgain()">Try another method</p>
    </app-form-template>
</app-main-screen>
