{"name": "navan", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.5", "@angular/cdk": "^19.0.4", "@angular/common": "^19.0.5", "@angular/compiler": "^19.0.5", "@angular/core": "^19.0.5", "@angular/forms": "^19.0.5", "@angular/material": "^19.0.4", "@angular/platform-browser": "^19.0.5", "@angular/platform-browser-dynamic": "^19.0.5", "@angular/router": "^19.0.5", "@primeng/themes": "^19.0.2", "crypto-js": "^4.2.0", "jsonwebtoken": "^9.0.2", "primeicons": "^7.0.0", "primeng": "^19.0.2", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "tslib": "^2.3.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.0", "@angular-devkit/architect": "^0.1900.6", "@angular-devkit/build-angular": "^19.0.5", "@angular-devkit/core": "^19.0.5", "@angular/cli": "^19.0.5", "@angular/compiler-cli": "^19.0.5", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.1.0", "javascript-obfuscator": "^4.1.1", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "webpack-obfuscator": "^3.5.1"}}