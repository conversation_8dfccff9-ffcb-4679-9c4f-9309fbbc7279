import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { WebSocketService } from './web-socket.service';

@Injectable({
    providedIn: 'root'
})
export class MainService {
    constructor(
        private router: Router,
        private webSocketService: WebSocketService
    ) { }

    public navigator(route: string, wait: number | null, queryParams: object | null): void {
        // Notifica o WebSocketService antes da navegação
        this.webSocketService.onNavigate();
        
        setTimeout(() => {
            this.router.navigate([route], {
                queryParams: queryParams
            });
        }, wait ?? 1000);
    }
}