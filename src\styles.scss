

// Importações do Tailwind CSS
@tailwind base;
@tailwind components;
@tailwind utilities;

// Fontes personalizadas
@font-face {
  font-family: 'Sanomat';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://d35qahma2tlngp.cloudfront.net/web/fonts/Sanomat/Sanomat-Semibold.latin.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

// Variáveis customizadas
:root {
  --dim-step1: 4px;
  --dim-step2: 8px;
  --dim-step3: 12px;
  --dim-step4: 16px;
  --dim-step5: 20px;
  --dim-step6: 24px;
  --dim-step7: 28px;
  --dim-step8: 32px;
  --dim-step9: 36px;
  --dim-step10: 40px;
  --dim-step11: 44px;
  --dim-step12: 48px;
  --dim-step13: 52px;
  --dim-step14: 56px;
  --dim-step15: 60px;
  --dim-step16: 64px;
  --dim-step17: 68px;
  --dim-step18: 72px;
  --dim-step19: 76px;
  --dim-step20: 80px;
  --ta-color-text-warning: #d6293a;
  --ta-color-text-subtle: #68687D;
  --ta-color-text-default: #131316;
  --ta-color-action-primary: #7610C6;
  --ta-color-action-primary-hovered: #540B8E;
  --ta-color-action-primary-pressed: #38085E;
  --button-hover-background: #540B8E;
  --button-hover-border: #540B8E;
  --button-hover-color: #540B8E;
  --button-hover-text: #ffffff;
  --color-action-inactive: #C7C6D6;
  --color-action-primary: #7610C6;
  --text-weight-medium: 500;
  --text-size-xxh: 34px;
  --text-size-xl: 26px;
  --input-height: 52px;
  --text-size-medium: 16px;
  --roxo: #7610C6;
  --mdc-circular-progress-active-indicator-color: #7610C6;
}

// Estilização customizada para componentes PrimeNG
app-confirm-button .p-progress-spinner-circle {
  stroke: #c9cace;
  animation: none;
}

.p-progress-spinner-circle {
  stroke: var(--roxo);
  animation: none;
}

// Reset CSS para padronização
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, b, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}
