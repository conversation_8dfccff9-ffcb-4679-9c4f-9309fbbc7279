.container {
  display: flex;
  width: 650px;
  height: 300px;
  margin: 0 auto;
  gap: 5px;
  position: relative;
  background: white;
  border-radius: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.title {
  margin-left: 40px;
  justify-items: center;
  margin-top: 30px;
  padding: calc(20px * 0.7) 0;
  letter-spacing: 0.02em;
  height: auto;
}

.title-h1 {
  font-family: Sanomat;
  line-height: calc(44px * 0.7);
  margin-bottom: calc(5px * 0.7);
  font-size: calc(var(--text-size-xxh) * 0.4);
  color: #333333;
}

.title-h60 {
  font-family: Sanomat;
  line-height: calc(44px * 0.7);
  margin-bottom: calc(5px * 0.7);
  font-size: calc(var(--text-size-xxh) * 0.5);
}

form {
  padding: calc(40px * 0.7) calc(40px * 0.7) calc(15px * 0.7);
}

.data-section {
  margin-bottom: calc(20px * 0.7);
}

.otp-code {
  width: 100%;
  height: calc(3.25rem * 0.7);
  border: 1px solid #c9cace;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: calc(10px * 0.7);
  padding: 0 calc(10px * 0.7);
  transition: all 0.3s ease;
  position: relative;

  &.filled {
    background-color: #f5f5f5;
    border-color: #f5f5f5;

    input {
      background-color: #f5f5f5;
      color: #65676e;
    }
    
    label {
      background-color: #f5f5f5;
      color: #65676e !important;
    }
  }

  .loading-indicator {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    position: absolute;
    left: calc(10px * 0.7);

    .spinner {
      width: 100%;
      height: 100%;
      border: 2px solid #7301cd;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }
  }

  .error-indicator {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: red;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: calc(16px * 0.7);
    animation: fadeOutQuick 0.5s ease 0.5s forwards;
    position: absolute;
    left: calc(10px * 0.7);
  }

  .code-input {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-left: calc(35px * 0.7);

    input {
      width: 100%;
      border: none;
      outline: none;
      border-radius: 10px;
      font-size: calc(16px * 0.7);
      padding: calc(15px * 0.7) 0 calc(14px * 0.7);
      transition: all 0.3s ease;
    }

    label {
      position: absolute;
      font-size: calc(16px * 0.7);
      color: #65676e;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.2s ease;
      padding: 0 calc(0.4rem * 0.7);
      background-color: white;
    }

    .floating {
      top: calc(-10px * 0.7);
      transform: translateY(0);
      color: #7301cd;
      font-size: calc(14px * 0.7);
    }
  }
}

.invalid-otp {
  font-size: calc(12px * 0.7);
  color: red;
  margin-top: calc(6px * 0.7);
  font-weight: 400;
}

.input-focus {
  border: calc(2px * 0.7) solid #7301cd;
}

.fixed-size-form {
  width: 650px;
  height: 300px;
  margin: 0 auto;
  background-color: transparent;
  position: relative;
}

.form-template {
  width: calc((650px / 3) * 2 - 2.5px);
  background-color: white;
  padding: calc(2rem * 0.7);
  border-radius: 24px 0 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .title-h1 {
    font-size: calc(1.2rem * 0.7);
    font-weight: 500;
    color: #5e5e5e;
    margin: 0;
  }
}

.magic-container {
  width: calc(650px / 3 - 2.5px);
  position: relative;
  height: 300px;

  ::ng-deep {
    app-magic {
      display: block;
      height: 100%;

      .magic-container {
        height: 100%;
      }

      .card {
        border-radius: 0 24px 24px 0;
        height: 100%;
        padding: calc(1.5rem * 0.7);
        display: flex;
        flex-direction: column;
      }

      .email-list {
        flex: 1;
        overflow: hidden;
      }

      .expand-content {
        top: calc(100% + 1px);
      }
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeOutQuick {
  to {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin: 0;
  }
}