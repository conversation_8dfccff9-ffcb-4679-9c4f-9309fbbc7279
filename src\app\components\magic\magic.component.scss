.magic-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.cursor {
  position: fixed;
  width: 32px;
  height: 32px;
  background-image: url('https://img.icons8.com/?size=100&id=jLkEh2zFhThT&format=png&color=000000');
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.8s ease;
  opacity: 0;
  
  &.clicking {
    transform: scale(0.9);
  }
  
  &.right-clicking {
    transform: scale(0.9);
  }
  
  &.context-menu {
    &::before {
      content: 'Copy magic link';
      position: absolute;
      left: 20px;
      top: 0;
      background: white;
      padding: 4px 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
  }
}

.card {
  width: 320px;
  background: white;
  border-radius: 24px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  .progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(115, 0, 205, 0.1);
    overflow: hidden;
  
    &.animating::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: #7300CD;
      transform: translateX(-100%);
      animation: progress 7.5s linear;
    }
  }
  
  @keyframes progress {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }
  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 1.5rem;
  }
}

.email-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.email-item {
  transition: all 0.5s ease;

  &.expanded {
    .email-row {
      padding-bottom: 0;
      border-bottom: none;
    }
  }
}

.email-row {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.left-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
  padding: 0.25rem 0;
}

.logo-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  &:not(img) {
    background: #7300CD;
    
    svg {
      width: 20px;
      height: auto;
    }
  }
}

.email-lines {
  flex: 1;

  .line {
    height: 0.8rem;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 0.5rem;

    &.short {
      width: 60%;
    }
  }
}

.email-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;

  .sender {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.775rem;
    font-weight: 500;
    color: #333;
    line-height: 1.2;

    .verified-icon {
      width: 14px;
      height: 14px;
      margin: 0 2px;
      display: block;

      polygon:first-child {
        fill: #42a5f5;
      }
      polygon:last-child {
        fill: #fff;
      }
    }

    .email-address {
      color: #666;
      font-weight: normal;
      font-size: 0.6125rem;
    }
  }

  .recipient {
    display: flex;
    align-items: center;
    gap: 2px;
    color: #666;
    font-size: 0.7125rem;
    line-height: 1.2;

    .arrow-icon {
      width: 10px;
      height: 10px;
      margin-left: 2px;
    }
  }
}

.expand-content {
  background: #f0fff7;
  margin-top: 0.5rem;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.5s ease;
}

.copied-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #7300CD;
  font-size: 0.63rem;
  padding: 0.35rem;
  animation: fadeIn 0.8s ease;

  .check-icon {
    width: 14px;
    height: 14px;
    fill: currentColor;
  }
}

.email-content {
  background: white;
  border-radius: 8px;
  padding: 0.7rem;
  margin: 0.35rem;
  animation: slideUp 0.8s ease;

  .email-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .logo-large {
    width: 84px;
    
    img {
      width: 100%;
      height: auto;
    }
  }

  h2 {
    font-size: 0.7rem;
    margin-bottom: 1rem;
    color: #000;
    text-align: center;
  }

  .magic-link {
    background: #7300CD;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    font-size: 0.56rem;
    cursor: pointer;
    width: 100%;
    transition: all 0.5s ease;
    
    &:hover {
      opacity: 0.9;
    }
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .magic-container {
    flex-direction: column;
    padding: 1rem;
  }

  .card {
    width: 100%;
  }
}