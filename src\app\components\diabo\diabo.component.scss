.template {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 570px;
    height: 25rem;
    max-height: 33.25rem;
    position: relative;
    // background-color: beige;
}

.return {
    width: fit-content;
    height: 30px;
    display: flex;
    align-items: center;
    align-self: flex-start;
    margin: 1rem 0 1rem 5rem;
    cursor: pointer;

    span {
        margin-left: 3px;
    }
}

.apsg {
    font-size: 24px;
    font-weight: 500;
    align-self: flex-start;
    margin: 0 0 2rem 5rem;
}

.code {
    position: relative;
    top: -63px;
    font-weight: bold;
    font-size: 32px;
}

.timeout {
    font-size: 18px;
    color: #d6293a;
}

.info {
    width: 60%; 
    align-self: flex-start;
    height: fit-content;
    display: flex;
    align-items: center;
    margin-left: 5rem;
    margin-bottom: 1rem;

    mat-icon {
        padding-right: 30px;
    }
}

.resend {
    font-size: 14px;
    color: var(--roxo);
    cursor: pointer;
}

.loadingggggggg {
    position: relative;
    // top: 38%;
}

@media (max-width: 570px) {
    .return, .apsg, .info {
        margin-left: 3rem;
    }

    // .loadingggggggg {
    //     top: 50%;
    // }
}