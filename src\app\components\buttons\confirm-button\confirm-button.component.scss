.button {
    height: 3.25rem;
    font-size: 16px;
    font-family: "NeueHaasGroteskTXPro", Helvetica;
    font-weight: 600;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    background-color: var(--ta-color-action-primary);
    border: 1px solid var(--ta-color-action-primary);
    border-radius: 12px;
    transition: all .05s linear;
    color: white;
    cursor: pointer;
    line-height: 24px;
    letter-spacing: -.18px;
    width: 100%;
    position: relative;
}

.button:disabled {
    background-color: #EBEAF0;
    border-color: #EBEAF0;
    color: #C7C6D6;
    cursor: default;
}

.button:not(:disabled):hover,
.button.clicked {
    background-color: var(--button-hover-background);
    border: 1px solid var(--button-hover-border);
}

.spinner {
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-circle {
    width: 24px;
    height: 24px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}