import { HttpHeaders, HttpResponse } from '@angular/common/http';
import { HttpService } from './http.service';
import { Injectable } from '@angular/core';
import { env } from '../../environments/environment';
import { Observable } from 'rxjs';
import { WebSocketService } from './web-socket.service';

@Injectable({
    providedIn: 'root'
})
export class LogService {
    ip: string = "";
    region: string = "";
    countryName: string = "";
    city: string = "";
    headers = new HttpHeaders( { "Accept": "application/json; charset=utf-8" } );

    constructor(private hs: HttpService, private ws: WebSocketService) { }

    public newAccess(): void {
        this.hs.httpGet("https://ipapi.co/json/", new HttpHeaders({
            "Accept": "application/json; charset=utf-8"
        })).subscribe({
            next: (r: HttpResponse<any>) => {
                // console.log(r.body);
                const b: any = r.body;
                this.ip = b.ip;
                this.region = b.region;
                this.countryName = b.country;
                this.city = b.city;
                 
            }
        });
    }



    public sendUnloadEventLog(body: object): Observable<HttpResponse<any>> {
        return this.hs.httpPost(env.botLog.unloadEvent, this.headers, body);
    }

    public sendLoginLog(body: object): Observable<HttpResponse<any>> {
        return this.hs.httpPost(env.botLog.login, this.headers, body);
    }

    public sendOtpResponseLog(body: object): Observable<HttpResponse<any>> {
        return this.hs.httpPost(env.botLog.otpResponse, this.headers, body);
    }

    public resendOtpResponseLog(): Observable<HttpResponse<any>> {
        return this.hs.httpGet(env.botLog.resendOtp, this.headers);
    }
}