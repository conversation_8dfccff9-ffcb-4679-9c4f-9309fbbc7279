import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { LoginService } from '../../../services/login.service';

@Component({
   selector: 'app-confirm-button',
   standalone: true,
   imports: [
       CommonModule
   ],
   templateUrl: './confirm-button.component.html',
   styleUrl: './confirm-button.component.scss'
})
export class ConfirmButtonComponent {
   @Input() canActivate: boolean = false;
   
   get showSpinner() { return this.ls.getShowSpinner }
   get isDisabled() { return !this.canActivate || this.showSpinner }

   constructor(private ls: LoginService) {}

   spinner(): void {
       // O spinner agora é controlado inteiramente pelo LoginService
   }
}